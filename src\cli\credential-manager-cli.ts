#!/usr/bin/env node
/**
 * CLI tool for managing Confluence credentials
 */

import { SecureCredentialsManager, ConfluenceCredential } from '../services/secure-credentials-manager';
import { askPassword } from '../utils/password-input';
import { logger } from '../utils/logger';
import { withErrorHandling } from '../utils/error-handler';
import * as readline from 'readline';
import * as path from 'path';
import * as fs from 'fs/promises';

export interface CLIOptions {
  importFilePath?: string;
  showHelp?: boolean;
}

let rl: readline.Interface | null = null;

function askQuestion(query: string): Promise<string> {
  if (!rl) {
    rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }
  return new Promise(resolve => rl!.question(query, resolve));
}

/**
 * Clean up readline interface (exported for testing)
 */
export function cleanupReadline(): void {
  if (rl) {
    rl.close();
    rl = null;
  }
}

/**
 * Get menu options (testable function)
 */
export function getMenuOptions(): string[] {
  return [
    '\nOptions:',
    '1. List all credentials',
    '2. Add/Update credential',
    '3. Remove credential',
    '4. Import from JSON file',
    '5. Export credentials to JSON',
    '6. Exit'
  ];
}

export type MenuAction = 'list' | 'add' | 'remove' | 'import' | 'export' | 'exit' | 'invalid';

/**
 * Parse menu choice (testable function)
 */
export function parseMenuChoice(choice: string): MenuAction {
  switch (choice.trim()) {
    case '1': return 'list';
    case '2': return 'add';
    case '3': return 'remove';
    case '4': return 'import';
    case '5': return 'export';
    case '6': return 'exit';
    default: return 'invalid';
  }
}

/**
 * Execute menu action (testable function)
 */
export async function executeMenuAction(action: MenuAction, manager: SecureCredentialsManager): Promise<void> {
  switch (action) {
    case 'list':
      listCredentialsToConsole(manager);
      break;
    case 'add':
      await addOrUpdateCredential(manager);
      break;
    case 'remove':
      await removeCredential(manager);
      break;
    case 'import':
      await importFromJsonFile(manager);
      break;
    case 'export':
      await exportCredentials(manager);
      break;
    default:
      // Should not happen if parseMenuChoice works correctly
      console.log('Unknown action');
  }
}

/**
 * Parse command line arguments
 */
export function parseArguments(args: string[]): CLIOptions {
  const options: CLIOptions = {};

  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--import' || args[i] === '-i') {
      if (i + 1 < args.length) {
        options.importFilePath = args[i + 1];
        i++; // Skip the next argument as it's the file path
      } else {
        throw new Error('--import flag requires a file path argument.');
      }
    } else if (args[i] === '--help' || args[i] === '-h') {
      options.showHelp = true;
    }
  }

  return options;
}

/**
 * Get help message sections (testable function)
 */
export function getHelpSections(): { usage: string; options: string[]; examples: string[] } {
  return {
    usage: 'Usage: credential-manager-cli [options]',
    options: [
      '  --import, -i <file>  Import credentials from specified JSON file',
      '  --help, -h           Show this help message'
    ],
    examples: [
      '  credential-manager-cli --import ./my-credentials.json',
      '  credential-manager-cli -i /path/to/config.json'
    ]
  };
}

/**
 * Format help message from sections (testable function)
 */
export function formatHelpMessage(sections: { usage: string; options: string[]; examples: string[] }): string {
  return [
    sections.usage,
    '',
    'Options:',
    ...sections.options,
    '',
    'Examples:',
    ...sections.examples
  ].join('\n');
}

/**
 * Get formatted help message
 */
export function getHelpMessage(): string {
  const sections = getHelpSections();
  return formatHelpMessage(sections);
}

/**
 * Validate JSON content structure (testable function)
 */
export function validateJsonCredentials(data: any): { isValid: boolean; error?: string; count?: number } {
  if (!Array.isArray(data)) {
    return { isValid: false, error: 'JSON must contain an array of credentials' };
  }

  if (data.length === 0) {
    return { isValid: false, error: 'JSON array cannot be empty' };
  }

  // Validate each credential object
  for (let i = 0; i < data.length; i++) {
    const cred = data[i];
    if (!cred || typeof cred !== 'object') {
      return { isValid: false, error: `Invalid credential at index ${i}: must be an object` };
    }

    if (!cred.name || typeof cred.name !== 'string') {
      return { isValid: false, error: `Invalid credential at index ${i}: missing or invalid 'name' field` };
    }

    if (!cred.baseUrl || typeof cred.baseUrl !== 'string') {
      return { isValid: false, error: `Invalid credential at index ${i}: missing or invalid 'baseUrl' field` };
    }

    if (!cred.spaceKey || typeof cred.spaceKey !== 'string') {
      return { isValid: false, error: `Invalid credential at index ${i}: missing or invalid 'spaceKey' field` };
    }
  }

  return { isValid: true, count: data.length };
}

/**
 * Import credentials from JSON file with enhanced validation
 */
export async function importCredentials(filePath: string): Promise<number> {
  // Check if file exists
  try {
    await fs.access(filePath);
  } catch (error) {
    throw new Error(`File not found: ${filePath}`);
  }

  // Read and parse file
  const fileContent = await fs.readFile(filePath, 'utf8');
  let parsedContent: any;

  try {
    parsedContent = JSON.parse(fileContent);
  } catch (error) {
    throw new Error('Invalid JSON format in file.');
  }

  // Handle both array format and object with credentials array
  let credentials: any[];
  if (Array.isArray(parsedContent)) {
    credentials = parsedContent;
  } else if (parsedContent && Array.isArray(parsedContent.credentials)) {
    credentials = parsedContent.credentials;
  } else {
    throw new Error('File must contain an array of credentials or an object with a credentials array.');
  }

  // Validate credentials using the new validation function
  const validation = validateJsonCredentials(credentials);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  return validation.count || 0;
}

async function main(): Promise<void> {
  console.log('Confluence Credential Manager');
  console.log('=============================');

  // Check for command-line arguments
  const args = process.argv.slice(2);

  try {
    await executeMainLogic(args);
  } catch (error) {
    handleMainError(error);
  } finally {
    // Ensure readline interface is closed in all cases
    cleanupReadline();
  }
}

/**
 * List all stored credentials (testable function)
 */
export function listCredentials(manager: SecureCredentialsManager): string[] {
  const credentials = manager.getCredentials();

  if (credentials.length === 0) {
    return ['No credentials stored.'];
  }

  const output: string[] = ['\nStored Credentials:\n'];
  credentials.forEach((cred, index) => {
    output.push(`${index + 1}. ${cred.name} (${cred.baseUrl})`);
    output.push(`   Space Key: ${cred.spaceKey}`);
    output.push(`   Auth Method: ${cred.puppeteerLogin ? 'Browser Login' : 'API Token'}`);
    if (cred.username) {
      output.push(`   Username: ${cred.username}`);
    }
    output.push(''); // Empty line for readability
  });

  return output;
}

/**
 * List credentials and print to console (for CLI usage)
 */
function listCredentialsToConsole(manager: SecureCredentialsManager): void {
  const output = listCredentials(manager);
  output.forEach(line => console.log(line));
}

/**
 * Create credential object from user input (testable function)
 */
export function createCredentialFromInput(
  name: string,
  baseUrl: string,
  spaceKey: string,
  authMethod: string,
  token?: string
): ConfluenceCredential {
  const puppeteerLogin = authMethod === '2';

  return {
    name,
    baseUrl,
    spaceKey,
    puppeteerLogin,
    token: puppeteerLogin ? undefined : token,
    username: undefined,
    password: undefined
  };
}

/**
 * Validate credential input (testable function)
 */
export function validateCredentialInput(
  name: string,
  baseUrl: string,
  spaceKey: string,
  authMethod: string,
  token?: string
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!name || name.trim().length === 0) {
    errors.push('Name is required');
  }

  if (!baseUrl || baseUrl.trim().length === 0) {
    errors.push('Base URL is required');
  } else {
    try {
      new URL(baseUrl);
    } catch {
      errors.push('Base URL must be a valid URL');
    }
  }

  if (!spaceKey || spaceKey.trim().length === 0) {
    errors.push('Space Key is required');
  }

  if (!authMethod || !['1', '2'].includes(authMethod)) {
    errors.push('Authentication Method must be 1 or 2');
  }

  if (authMethod === '1' && (!token || token.trim().length === 0)) {
    errors.push('API Token is required for API Token authentication');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Format add credential header (testable function)
 */
export function formatAddCredentialHeader(): string[] {
  return [
    '\nAdd/Update Credential',
    '===================='
  ];
}

/**
 * Format validation errors (testable function)
 */
export function formatValidationErrors(errors: string[]): string[] {
  const result = ['Validation errors:'];
  errors.forEach(error => result.push(`- ${error}`));
  return result;
}

/**
 * Format success message (testable function)
 */
export function formatSuccessMessage(name: string, action: 'saved' | 'removed' | 'exported' | 'imported'): string {
  switch (action) {
    case 'saved':
      return `Credential for ${name} saved successfully.`;
    case 'removed':
      return `Credential for ${name} removed successfully.`;
    case 'exported':
      return 'Export completed successfully!';
    case 'imported':
      return `Successfully imported ${name} credential(s).`;
    default:
      return `Operation completed successfully for ${name}.`;
  }
}

/**
 * Format file not found error (testable function)
 */
export function formatFileNotFoundError(filePath: string, isProvidedPath: boolean): string[] {
  const messages = [
    `Error: File not found: ${filePath}`,
    'Please check the file path and try again.'
  ];

  if (!isProvidedPath) {
    messages.push('You can try again with a different file path.');
  }

  return messages;
}

/**
 * Format non-JSON warning (testable function)
 */
export function formatNonJsonWarning(filePath: string): string {
  return `Warning: File "${filePath}" does not have a .json extension.`;
}

/**
 * Format JSON validation error (testable function)
 */
export function formatJsonValidationError(isProvidedPath: boolean): string[] {
  const messages = [
    'Error: JSON file must contain an array of credential objects.',
    'Expected format: [{"name": "...", "baseUrl": "...", "spaceKey": "...", ...}, ...]'
  ];

  if (!isProvidedPath) {
    messages.push('Please check your file format and try again.');
  }

  return messages;
}

/**
 * Check if user wants to proceed (testable function)
 */
export function shouldProceedWithOperation(userInput: string): boolean {
  return userInput.toLowerCase() === 'y';
}

/**
 * Handle CLI help display (testable function)
 */
export function handleHelpDisplay(showHelp: boolean): boolean {
  if (showHelp) {
    console.log(getHelpMessage());
    return true; // Indicates help was shown and execution should stop
  }
  return false;
}

/**
 * Handle import file path execution (testable function)
 */
export async function handleImportFilePath(manager: SecureCredentialsManager, importFilePath: string): Promise<void> {
  console.log(`\nImporting from: ${importFilePath}`);
  await importFromJsonFile(manager, importFilePath);
  await manager.cleanup();
  cleanupReadline();
}

/**
 * Handle initialization failure (testable function)
 */
export function handleInitializationFailure(initResult: { success: boolean; cancelled?: boolean }): boolean {
  if (!initResult.success) {
    if (initResult.cancelled) {
      console.log('\nOperation cancelled by user.');
    } else {
      console.error('Failed to initialize credentials manager after maximum attempts.');
    }
    cleanupReadline();
    return true; // Indicates failure and execution should stop
  }
  return false;
}

/**
 * Handle menu loop exit (testable function)
 */
export async function handleMenuExit(manager: SecureCredentialsManager): Promise<void> {
  console.log('Exiting...');
  await manager.cleanup();
  cleanupReadline();
}

/**
 * Handle invalid menu choice (testable function)
 */
export function handleInvalidMenuChoice(): string {
  console.log('Invalid option. Please try again.');
  return 'continue'; // Indicates the loop should continue
}

/**
 * Check if file has JSON extension (testable function)
 */
export function hasJsonExtension(filePath: string): boolean {
  return filePath.toLowerCase().endsWith('.json');
}

/**
 * Validate JSON content structure (testable function)
 */
export function validateJsonContent(content: any): { isValid: boolean; error?: string } {
  if (!Array.isArray(content)) {
    return {
      isValid: false,
      error: 'JSON file must contain an array of credential objects.'
    };
  }

  if (content.length === 0) {
    return {
      isValid: false,
      error: 'JSON file contains an empty array. No credentials to import.'
    };
  }

  // Validate each credential structure
  for (let i = 0; i < content.length; i++) {
    const cred = content[i];
    if (!cred.name || !cred.baseUrl || !cred.spaceKey) {
      return {
        isValid: false,
        error: `Invalid credential at index ${i}. Missing required fields: name, baseUrl, or spaceKey.`
      };
    }
  }

  return { isValid: true };
}

/**
 * Format credential count message (testable function)
 */
export function formatCredentialCountMessage(count: number): string {
  return `Found ${count} valid credential(s) in the file.`;
}

/**
 * Format import confirmation message (testable function)
 */
export function formatImportConfirmationMessage(): string {
  return 'Proceed with import? This will replace all existing credentials. (y/n): ';
}

/**
 * Format import cancellation message (testable function)
 */
export function formatImportCancellationMessage(): string {
  return 'Import cancelled.';
}

/**
 * Format import success message (testable function)
 */
export function formatImportSuccessMessage(): string {
  return 'Import successful!';
}

/**
 * Format CLI import message (testable function)
 */
export function formatCliImportMessage(): string {
  return 'Proceeding with import as requested via command line...';
}

/**
 * Format non-JSON continuation message (testable function)
 */
export function formatNonJsonContinuationMessage(): string {
  return 'Continuing with non-JSON file as requested...';
}

/**
 * Handle JSON parsing error (testable function)
 */
export function handleJsonParsingError(error: any, isProvidedPath: boolean): string[] {
  const messages: string[] = [];

  if (error instanceof SyntaxError) {
    messages.push('Error: Invalid JSON format in file.');
    messages.push('Please ensure the file contains valid JSON data.');
  } else {
    messages.push('Import failed:', error instanceof Error ? error.message : error);
  }

  if (!isProvidedPath) {
    messages.push('You can try again with a different file.');
  }

  return messages;
}

/**
 * Format export header (testable function)
 */
export function formatExportHeader(): string[] {
  return [
    '\nExport Credentials',
    '=================='
  ];
}

/**
 * Format export warning message (testable function)
 */
export function formatExportWarningMessage(defaultPath: string): string {
  return `Enter export file path [${defaultPath}]: `;
}

/**
 * Format export security warning (testable function)
 */
export function formatExportSecurityWarning(): string {
  return 'Warning: Exported file will contain sensitive data in plain text. Continue? (y/n): ';
}

/**
 * Format export cancellation message (testable function)
 */
export function formatExportCancellationMessage(): string {
  return 'Export cancelled.';
}

/**
 * Format export completion message (testable function)
 */
export function formatExportCompletionMessage(): string[] {
  return [
    'Export completed successfully!',
    'Remember to store the exported file securely and delete it when no longer needed.'
  ];
}

/**
 * Format export error message (testable function)
 */
export function formatExportErrorMessage(error: any): string[] {
  return [
    'Export failed:',
    error instanceof Error ? error.message : error
  ];
}

/**
 * Generate timestamped export path (testable function)
 */
export function generateTimestampedExportPath(): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `./credentials-export-${timestamp}.json`;
}

/**
 * Format file access error (testable function)
 */
export function formatFileAccessError(filePath: string, isProvidedPath: boolean): string[] {
  const messages = [`Error: File not found: ${filePath}`];

  if (isProvidedPath) {
    messages.push('Please check the file path and try again.');
  } else {
    messages.push('Please check the file path and try again.');
    messages.push('You can try again with a different file path.');
  }

  return messages;
}

/**
 * Handle CLI import mode (testable function)
 */
export async function handleCliImportMode(manager: SecureCredentialsManager, importFilePath: string): Promise<void> {
  console.log(`\nImporting from: ${importFilePath}`);
  await importFromJsonFile(manager, importFilePath);
  await manager.cleanup();
  cleanupReadline();
}

/**
 * Resume stdin if paused (testable function)
 */
export function resumeStdinIfPaused(): void {
  if (process.stdin.isPaused()) {
    process.stdin.resume();
  }
}

/**
 * Display menu options (testable function)
 */
export function displayMenuOptions(): void {
  const menuOptions = getMenuOptions();
  menuOptions.forEach(option => console.log(option));
}

/**
 * Handle menu loop iteration (testable function)
 */
export async function handleMenuLoopIteration(manager: SecureCredentialsManager): Promise<'continue' | 'exit'> {
  displayMenuOptions();

  const choice = await askQuestion('Select an option (1-6): ');
  const action = parseMenuChoice(choice);

  if (action === 'exit') {
    await handleMenuExit(manager);
    return 'exit';
  }

  if (action === 'invalid') {
    handleInvalidMenuChoice();
    return 'continue';
  }

  // Execute the chosen action
  await executeMenuAction(action, manager);
  return 'continue';
}

/**
 * Handle main error (testable function)
 */
export function handleMainError(error: any): void {
  console.error('Error:', error instanceof Error ? error.message : error);
  console.error('Usage: credential-manager-cli --import <file-path>');
  console.error('       credential-manager-cli --help');
}

/**
 * Format export path prompt (testable function)
 */
export function formatExportPathPrompt(defaultPath: string): string {
  return `Enter export file path [${defaultPath}]: `;
}

/**
 * Format password prompt for initialization (testable function)
 */
export function formatInitPasswordPrompt(attempts: number, maxAttempts: number): string {
  return `Enter master password${attempts > 1 ? ` (attempt ${attempts}/${maxAttempts})` : ''}: `;
}

/**
 * Format initialization error messages (testable function)
 */
export function formatInitErrorMessages(error: any): string[] {
  return [
    `\nFailed to initialize credentials manager: ${error instanceof Error ? error.message : error}`
  ];
}

/**
 * Format initialization retry messages (testable function)
 */
export function formatInitRetryMessages(): string[] {
  return [
    'This could be due to:',
    '- Incorrect password',
    '- Missing or corrupted key files',
    '- File permission issues',
    '\nPlease try again...\n'
  ];
}

/**
 * Format initialization max attempts messages (testable function)
 */
export function formatInitMaxAttemptsMessages(maxAttempts: number): string[] {
  return [
    `\nMaximum attempts (${maxAttempts}) reached. Exiting...`,
    'Please check your password and ensure the credential files are accessible.'
  ];
}

/**
 * Check if password indicates cancellation (testable function)
 */
export function isPasswordCancelled(password: string): boolean {
  return password === '';
}

/**
 * Format credential removal prompt (testable function)
 */
export function formatCredentialRemovalPrompt(credentialName: string): string {
  return `Are you sure you want to remove "${credentialName}"? (y/n): `;
}

/**
 * Format credential removal success message (testable function)
 */
export function formatCredentialRemovalSuccessMessage(credentialName: string): string {
  return `Credential for ${credentialName} removed successfully.`;
}

/**
 * Format operation cancelled message (testable function)
 */
export function formatOperationCancelledMessage(): string {
  return 'Operation cancelled.';
}

/**
 * Format no credentials to remove message (testable function)
 */
export function formatNoCredentialsToRemoveMessage(): string {
  return 'No credentials to remove.';
}

/**
 * Format credential selection prompt (testable function)
 */
export function formatCredentialSelectionPrompt(credentialsCount: number): string {
  return `Enter number (1-${credentialsCount}): `;
}

/**
 * Validate removal confirmation (testable function)
 */
export function validateRemovalConfirmation(confirmation: string): boolean {
  return confirmation.toLowerCase() === 'y';
}

/**
 * Format credential input prompts (testable function)
 */
export function formatCredentialInputPrompts(): {
  name: string;
  baseUrl: string;
  spaceKey: string;
  authMethod: string;
  apiToken: string;
} {
  return {
    name: 'Name (e.g., "Company Confluence"): ',
    baseUrl: 'Base URL (e.g., "https://confluence.example.com"): ',
    spaceKey: 'Space Key: ',
    authMethod: 'Authentication Method (1 for API Token, 2 for Browser Login): ',
    apiToken: 'API Token: '
  };
}

/**
 * Execute main CLI logic (testable function)
 */
export async function executeMainLogic(args: string[]): Promise<void> {
  const options = parseArguments(args);

  if (options.showHelp) {
    console.log(getHelpMessage());
    return;
  }

  // Continue with the rest of the logic
  const importFilePath = options.importFilePath;

  // Initialize credentials manager with retry mechanism
  const manager = new SecureCredentialsManager();
  const initResult = await initializeManagerWithRetry(manager);

  if (!initResult.success) {
    if (initResult.cancelled) {
      console.log('\nOperation cancelled by user.');
    } else {
      console.error('Failed to initialize credentials manager after maximum attempts.');
    }
    cleanupReadline();
    return;
  }

  // If import file path was provided via command line, import directly
  if (importFilePath) {
    console.log(`\nImporting from: ${importFilePath}`);
    await importFromJsonFile(manager, importFilePath);
    await manager.cleanup();
    cleanupReadline();
    return;
  }

  // Ensure stdin is resumed before showing the menu
  if (process.stdin.isPaused()) {
    process.stdin.resume();
  }

  while (true) {
    const menuOptions = getMenuOptions();
    menuOptions.forEach(option => console.log(option));

    const choice = await askQuestion('Select an option (1-6): ');
    const action = parseMenuChoice(choice);

    if (action === 'exit') {
      console.log('Exiting...');
      await manager.cleanup();
      cleanupReadline();
      return;
    }

    if (action === 'invalid') {
      console.log('Invalid option. Please try again.');
      continue;
    }

    // Execute the chosen action
    await executeMenuAction(action, manager);
  }
}

async function addOrUpdateCredential(manager: SecureCredentialsManager): Promise<void> {
  return withErrorHandling(async () => {
    const header = formatAddCredentialHeader();
    header.forEach(line => console.log(line));

    const name = await askQuestion('Name (e.g., "Company Confluence"): ');
    const baseUrl = await askQuestion('Base URL (e.g., "https://confluence.example.com"): ');
    const spaceKey = await askQuestion('Space Key: ');
    const authMethod = await askQuestion('Authentication Method (1 for API Token, 2 for Browser Login): ');

    let token: string | undefined;
    if (authMethod === '1') {
      token = await askQuestion('API Token: ');
    }

    // Validate input
    const validation = validateCredentialInput(name, baseUrl, spaceKey, authMethod, token);
    if (!validation.isValid) {
      const errorMessages = formatValidationErrors(validation.errors);
      errorMessages.forEach(msg => console.error(msg));
      return;
    }

    const credential = createCredentialFromInput(name, baseUrl, spaceKey, authMethod, token);

    await manager.addOrUpdateCredential(credential);
    console.log(formatSuccessMessage(name, 'saved'));
  }, {
    context: 'credential addition/update',
    exitOnError: false,
    logger
  });
}

/**
 * Parse user selection for credential removal (testable function)
 */
export function parseCredentialSelection(
  choice: string,
  credentialsCount: number
): { isValid: boolean; index: number; error?: string } {
  // Check if input is a valid integer (no decimals, no non-numeric characters)
  if (!/^\d+$/.test(choice.trim())) {
    return { isValid: false, index: -1, error: 'Selection must be a number' };
  }

  const index = parseInt(choice, 10) - 1;

  if (index < 0 || index >= credentialsCount) {
    return { isValid: false, index: -1, error: `Selection must be between 1 and ${credentialsCount}` };
  }

  return { isValid: true, index };
}

/**
 * Format credentials for selection display (testable function)
 */
export function formatCredentialsForSelection(credentials: ConfluenceCredential[]): string[] {
  if (credentials.length === 0) {
    return ['No credentials available.'];
  }

  const output = ['\nSelect credential:'];
  credentials.forEach((cred, index) => {
    output.push(`${index + 1}. ${cred.name} (${cred.baseUrl})`);
  });

  return output;
}

async function removeCredential(manager: SecureCredentialsManager): Promise<void> {
  return withErrorHandling(async () => {
    const credentials = manager.getCredentials();

    if (credentials.length === 0) {
      console.log('No credentials to remove.');
      return;
    }

    const selectionDisplay = formatCredentialsForSelection(credentials);
    selectionDisplay.forEach(line => console.log(line));

    const choice = await askQuestion(`Enter number (1-${credentials.length}): `);
    const selection = parseCredentialSelection(choice, credentials.length);

    if (!selection.isValid) {
      console.log(selection.error);
      return;
    }

    const credential = credentials[selection.index];
    const confirmed = await askQuestion(`Are you sure you want to remove "${credential.name}"? (y/n): `);

    if (confirmed.toLowerCase() === 'y') {
      await manager.removeCredential(credential.baseUrl);
      console.log(`Credential for ${credential.name} removed successfully.`);
    } else {
      console.log('Operation cancelled.');
    }
  }, {
    context: 'credential removal',
    exitOnError: false,
    logger
  });
}

/**
 * Generate default import file path (testable function)
 */
export function generateDefaultImportPath(): string {
  return path.join(process.cwd(), 'config.json');
}

/**
 * Validate import file path (testable function)
 */
export function validateImportFilePath(filePath: string): { isValid: boolean; error?: string } {
  if (!filePath || filePath.trim().length === 0) {
    return { isValid: false, error: 'File path cannot be empty' };
  }

  if (!filePath.toLowerCase().endsWith('.json')) {
    return { isValid: false, error: 'File must be a JSON file (.json)' };
  }

  return { isValid: true };
}

/**
 * Resolve import file path (testable function)
 */
export function resolveImportFilePath(providedPath?: string, userInput?: string): string {
  if (providedPath) {
    return providedPath;
  }

  const defaultPath = generateDefaultImportPath();
  return (userInput && userInput.trim()) || defaultPath;
}

/**
 * Format import instructions (testable function)
 */
export function formatImportInstructions(defaultPath: string): string[] {
  return [
    '\nImport from JSON File',
    '====================',
    'You can import credentials from any JSON file containing an array of credential objects.',
    `Default file: ${defaultPath}`
  ];
}

export async function importFromJsonFile(manager: SecureCredentialsManager, providedPath?: string): Promise<void> {
  return withErrorHandling(async () => {
    const defaultPath = generateDefaultImportPath();
    let targetPath: string;

    if (providedPath) {
      targetPath = providedPath;
    } else {
      // Interactive mode
      const instructions = formatImportInstructions(defaultPath);
      instructions.forEach(line => console.log(line));

      const filePath = await askQuestion(`Enter path to JSON file [${defaultPath}]: `);
      targetPath = resolveImportFilePath(undefined, filePath);
    }

    // Validate file path format
    const pathValidation = validateImportFilePath(targetPath);
    if (!pathValidation.isValid) {
      console.error(`Error: ${pathValidation.error}`);
      return;
    }

    // Validate file existence
    try {
      await fs.access(targetPath);
    } catch (error) {
      console.error(`Error: File not found: ${targetPath}`);
      console.error('Please check the file path and try again.');
      if (!providedPath) {
        console.log('You can try again with a different file path.');
      }
      return;
    }

    // Check if file extension warning is needed
    if (!targetPath.toLowerCase().endsWith('.json')) {
      console.warn(`Warning: File "${targetPath}" does not have a .json extension.`);
      if (!providedPath) {
        const proceed = await askQuestion('Continue anyway? (y/n): ');
        if (proceed.toLowerCase() !== 'y') {
          console.log('Import cancelled.');
          return;
        }
      } else {
        console.log('Continuing with non-JSON file as requested...');
      }
    }

    // Validate JSON format before importing
    try {
      const fileContent = await fs.readFile(targetPath, 'utf-8');
      const parsedContent = JSON.parse(fileContent);

      if (!Array.isArray(parsedContent)) {
        console.error('Error: JSON file must contain an array of credential objects.');
        console.error('Expected format: [{"name": "...", "baseUrl": "...", "spaceKey": "...", ...}, ...]');
        if (!providedPath) {
          console.log('Please check the file format and try again.');
        }
        return;
      }

      if (parsedContent.length === 0) {
        console.error('Error: JSON file contains an empty array. No credentials to import.');
        if (!providedPath) {
          console.log('Please use a file with valid credentials.');
        }
        return;
      }

      // Validate credential structure
      for (let i = 0; i < parsedContent.length; i++) {
        const cred = parsedContent[i];
        if (!cred.name || !cred.baseUrl || !cred.spaceKey) {
          console.error(`Error: Invalid credential at index ${i}. Missing required fields: name, baseUrl, or spaceKey.`);
          console.error('Each credential must have at least: {"name": "...", "baseUrl": "...", "spaceKey": "..."}');
          if (!providedPath) {
            console.log('Please fix the credential format and try again.');
          }
          return;
        }
      }

      console.log(`Found ${parsedContent.length} valid credential(s) in the file.`);

      if (!providedPath) {
        const proceed = await askQuestion('Proceed with import? This will replace all existing credentials. (y/n): ');
        if (proceed.toLowerCase() !== 'y') {
          console.log('Import cancelled.');
          return;
        }
      } else {
        console.log('Proceeding with import as requested via command line...');
      }

      await manager.importFromFile(targetPath);
      console.log('Import successful!');
    } catch (error) {
      if (error instanceof SyntaxError) {
        console.error('Error: Invalid JSON format in file.');
        console.error('Please ensure the file contains valid JSON data.');
      } else {
        console.error('Import failed:', error instanceof Error ? error.message : error);
      }
      if (!providedPath) {
        console.log('You can try again with a different file.');
      }
    }
  }, {
    context: 'credential import',
    exitOnError: false,
    logger
  });
}

/**
 * Generate default export file path (testable function)
 */
export function generateDefaultExportPath(): string {
  return path.join(process.cwd(), 'credentials-export.json');
}

/**
 * Validate export confirmation (testable function)
 */
export function validateExportConfirmation(confirmation: string): boolean {
  return confirmation.toLowerCase() === 'y';
}

/**
 * Format export summary message (testable function)
 */
export function formatExportSummary(credentialsCount: number): string {
  if (credentialsCount === 0) {
    return 'No credentials to export.';
  }
  return `Found ${credentialsCount} credential(s) to export.`;
}

export interface ManagerInitResult {
  success: boolean;
  cancelled?: boolean;
}

/**
 * Initialize manager with retry mechanism (testable function)
 */
export async function initializeManagerWithRetry(manager: SecureCredentialsManager): Promise<ManagerInitResult> {
  let initialized = false;
  let attempts = 0;
  const maxAttempts = 3;

  while (!initialized && attempts < maxAttempts) {
    attempts++;
    const password = await askPassword(
      `Enter master password${attempts > 1 ? ` (attempt ${attempts}/${maxAttempts})` : ''}: `
    );

    // Handle Ctrl+C (empty password returned)
    if (password === '') {
      return { success: false, cancelled: true };
    }

    try {
      await manager.initialize(password);
      initialized = true;
    } catch (error) {
      console.error(`\nFailed to initialize credentials manager: ${error instanceof Error ? error.message : error}`);
      if (attempts < maxAttempts) {
        console.log('This could be due to:');
        console.log('- Incorrect password');
        console.log('- Missing or corrupted key files');
        console.log('- File permission issues');
        console.log('\nPlease try again...\n');
      } else {
        console.error(`\nMaximum attempts (${maxAttempts}) reached. Exiting...`);
        console.error('Please check your password and ensure the credential files are accessible.');
      }
    }
  }

  return { success: initialized, cancelled: false };
}

async function exportCredentials(manager: SecureCredentialsManager): Promise<void> {
  return withErrorHandling(async () => {
    console.log('\nExport Credentials');
    console.log('==================');

    const credentials = manager.getCredentials();
    const summary = formatExportSummary(credentials.length);
    console.log(summary);

    if (credentials.length === 0) {
      return;
    }

    const defaultPath = generateDefaultExportPath();
    const filePath = await askQuestion(`Enter export file path [${defaultPath}]: `);
    const confirmed = await askQuestion('Warning: Exported file will contain sensitive data in plain text. Continue? (y/n): ');

    if (!validateExportConfirmation(confirmed)) {
      console.log('Export cancelled.');
      return;
    }

    try {
      await manager.exportToFile(filePath || defaultPath);
      console.log('Export completed successfully!');
      console.log('Remember to store the exported file securely and delete it when no longer needed.');
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, {
    context: 'credential export',
    exitOnError: false,
    logger
  });
}

main().catch(error => {
  console.error('\nUnexpected error occurred:', error instanceof Error ? error.message : error);
  console.error('The application will now exit. Please try again.');
  cleanupReadline();
});