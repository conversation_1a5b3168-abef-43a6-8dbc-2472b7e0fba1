#!/usr/bin/env node
/**
 * CLI tool for managing Confluence credentials
 */

import { SecureCredentialsManager, ConfluenceCredential } from '../services/secure-credentials-manager';
import { askPassword } from '../utils/password-input';
import { logger } from '../utils/logger';
import { withErrorHandling } from '../utils/error-handler';
import * as readline from 'readline';
import * as path from 'path';
import * as fs from 'fs/promises';

let rl: readline.Interface;

function askQuestion(query: string): Promise<string> {
  if (!rl) {
    rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }
  return new Promise(resolve => rl.question(query, resolve));
}

async function main(): Promise<void> {
  console.log('Confluence Credential Manager');
  console.log('=============================');

  // Check for command-line arguments
  const args = process.argv.slice(2);
  let importFilePath: string | undefined;

  // Parse command-line arguments
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--import' || args[i] === '-i') {
      if (i + 1 < args.length) {
        importFilePath = args[i + 1];
        i++; // Skip the next argument as it's the file path
      } else {
        console.error('Error: --import flag requires a file path argument.');
        console.error('Usage: credential-manager-cli --import <file-path>');
        return;
      }
    } else if (args[i] === '--help' || args[i] === '-h') {
      console.log('Usage: credential-manager-cli [options]');
      console.log('');
      console.log('Options:');
      console.log('  --import, -i <file>  Import credentials from specified JSON file');
      console.log('  --help, -h           Show this help message');
      console.log('');
      console.log('Examples:');
      console.log('  credential-manager-cli --import ./my-credentials.json');
      console.log('  credential-manager-cli -i /path/to/config.json');
      return;
    }
  }

  // Initialize credentials manager with retry mechanism
  const manager = new SecureCredentialsManager();
  let initialized = false;
  let attempts = 0;
  const maxAttempts = 3;

  while (!initialized && attempts < maxAttempts) {
    attempts++;
    const password = await askPassword(
      `Enter master password${attempts > 1 ? ` (attempt ${attempts}/${maxAttempts})` : ''}: `
    );

    // Handle Ctrl+C (empty password returned)
    if (password === '') {
      console.log('\nOperation cancelled by user.');
      return;
    }

    try {
      await manager.initialize(password);
      initialized = true;
    } catch (error) {
      console.error(`\nFailed to initialize credentials manager: ${error instanceof Error ? error.message : error}`);
      if (attempts < maxAttempts) {
        console.log('This could be due to:');
        console.log('- Incorrect password');
        console.log('- Missing or corrupted key files');
        console.log('- File permission issues');
        console.log('\nPlease try again...\n');
      } else {
        console.error(`\nMaximum attempts (${maxAttempts}) reached. Exiting...`);
        console.error('Please check your password and ensure the credential files are accessible.');
        return;
      }
    }
  }

  // If import file path was provided via command line, import directly
  if (importFilePath) {
    console.log(`\nImporting from: ${importFilePath}`);
    await importFromJsonFile(manager, importFilePath);
    await manager.cleanup();
    return;
  }

  // Ensure stdin is resumed before showing the menu
  if (process.stdin.isPaused()) {
    process.stdin.resume();
  }

  while (true) {
    console.log('\nOptions:');
    console.log('1. List all credentials');
    console.log('2. Add/Update credential');
    console.log('3. Remove credential');
    console.log('4. Import from JSON file');
    console.log('5. Export credentials to JSON');
    console.log('6. Exit');

    const choice = await askQuestion('Select an option (1-6): ');

    switch (choice) {
      case '1':
        listCredentials(manager);
        break;
      case '2':
        await addOrUpdateCredential(manager);
        break;
      case '3':
        await removeCredential(manager);
        break;
      case '4':
        await importFromJsonFile(manager);
        break;
      case '5':
        await exportCredentials(manager);
        break;
      case '6':
        console.log('Exiting...');
        await manager.cleanup();
        return;
      default:
        console.log('Invalid option. Please try again.');
    }
  }
}

function listCredentials(manager: SecureCredentialsManager): void {
  const credentials = manager.getCredentials();
  
  if (credentials.length === 0) {
    console.log('No credentials stored.');
    return;
  }

  console.log('\nStored Credentials:');
  credentials.forEach((cred, index) => {
    console.log(`${index + 1}. ${cred.name} (${cred.baseUrl})`);
    console.log(`   Space Key: ${cred.spaceKey}`);
    console.log(`   Auth Method: ${cred.puppeteerLogin ? 'Browser Login' : 'API Token'}`);
    if (cred.username) {
      console.log(`   Username: ${cred.username}`);
    }
    console.log(''); // Empty line for readability
  });
}

async function addOrUpdateCredential(manager: SecureCredentialsManager): Promise<void> {
  return withErrorHandling(async () => {
    console.log('\nAdd/Update Credential');
    console.log('====================');

    const name = await askQuestion('Name (e.g., "Company Confluence"): ');
    const baseUrl = await askQuestion('Base URL (e.g., "https://confluence.example.com"): ');
    const spaceKey = await askQuestion('Space Key: ');
    const authMethod = await askQuestion('Authentication Method (1 for API Token, 2 for Browser Login): ');

    const puppeteerLogin = authMethod === '2';
    let token: string | undefined;
    let username: string | undefined;
    let password: string | undefined;

    if (!puppeteerLogin) {
      token = await askQuestion('API Token: ');
    }

    const credential: ConfluenceCredential = {
      name,
      baseUrl,
      spaceKey,
      puppeteerLogin,
      token,
      username,
      password: password || undefined
    };

    await manager.addOrUpdateCredential(credential);
    console.log(`Credential for ${name} saved successfully.`);
  }, {
    context: 'credential addition/update',
    exitOnError: false,
    logger
  });
}

async function removeCredential(manager: SecureCredentialsManager): Promise<void> {
  return withErrorHandling(async () => {
    const credentials = manager.getCredentials();
    
    if (credentials.length === 0) {
      console.log('No credentials to remove.');
      return;
    }

    console.log('\nSelect credential to remove:');
    credentials.forEach((cred, index) => {
      console.log(`${index + 1}. ${cred.name} (${cred.baseUrl})`);
    });

    const choice = await askQuestion(`Enter number (1-${credentials.length}): `);
    const index = parseInt(choice, 10) - 1;

    if (isNaN(index) || index < 0 || index >= credentials.length) {
      console.log('Invalid selection.');
      return;
    }

    const credential = credentials[index];
    const confirmed = await askQuestion(`Are you sure you want to remove "${credential.name}"? (y/n): `);

    if (confirmed.toLowerCase() === 'y') {
      await manager.removeCredential(credential.baseUrl);
      console.log(`Credential for ${credential.name} removed successfully.`);
    } else {
      console.log('Operation cancelled.');
    }
  }, {
    context: 'credential removal',
    exitOnError: false,
    logger
  });
}

async function importFromJsonFile(manager: SecureCredentialsManager, providedPath?: string): Promise<void> {
  return withErrorHandling(async () => {
    const defaultPath = path.join(process.cwd(), 'config.json');
    let targetPath: string;

    if (providedPath) {
      // Use the provided path from command line
      targetPath = providedPath;
    } else {
      // Interactive mode
      console.log('\nImport from JSON File');
      console.log('====================');
      console.log('You can import credentials from any JSON file containing an array of credential objects.');
      console.log(`Default file: ${defaultPath}`);
      const filePath = await askQuestion(`Enter path to JSON file [${defaultPath}]: `);
      targetPath = filePath.trim() || defaultPath;
    }

    // Validate file existence
    try {
      await fs.access(targetPath);
    } catch (error) {
      console.error(`Error: File not found: ${targetPath}`);
      console.error('Please check the file path and try again.');
      if (!providedPath) {
        console.log('You can try again with a different file path.');
      }
      return;
    }

    // Validate file extension
    if (!targetPath.toLowerCase().endsWith('.json')) {
      console.warn(`Warning: File "${targetPath}" does not have a .json extension.`);
      if (!providedPath) {
        const proceed = await askQuestion('Continue anyway? (y/n): ');
        if (proceed.toLowerCase() !== 'y') {
          console.log('Import cancelled.');
          return;
        }
      } else {
        console.log('Continuing with non-JSON file as requested...');
      }
    }

    // Validate JSON format before importing
    try {
      const fileContent = await fs.readFile(targetPath, 'utf-8');
      const parsedContent = JSON.parse(fileContent);

      if (!Array.isArray(parsedContent)) {
        console.error('Error: JSON file must contain an array of credential objects.');
        console.error('Expected format: [{"name": "...", "baseUrl": "...", "spaceKey": "...", ...}, ...]');
        if (!providedPath) {
          console.log('Please check the file format and try again.');
        }
        return;
      }

      if (parsedContent.length === 0) {
        console.error('Error: JSON file contains an empty array. No credentials to import.');
        if (!providedPath) {
          console.log('Please use a file with valid credentials.');
        }
        return;
      }

      // Validate credential structure
      for (let i = 0; i < parsedContent.length; i++) {
        const cred = parsedContent[i];
        if (!cred.name || !cred.baseUrl || !cred.spaceKey) {
          console.error(`Error: Invalid credential at index ${i}. Missing required fields: name, baseUrl, or spaceKey.`);
          console.error('Each credential must have at least: {"name": "...", "baseUrl": "...", "spaceKey": "..."}');
          if (!providedPath) {
            console.log('Please fix the credential format and try again.');
          }
          return;
        }
      }

      console.log(`Found ${parsedContent.length} valid credential(s) in the file.`);
      
      if (!providedPath) {
        const proceed = await askQuestion('Proceed with import? This will replace all existing credentials. (y/n): ');
        if (proceed.toLowerCase() !== 'y') {
          console.log('Import cancelled.');
          return;
        }
      } else {
        console.log('Proceeding with import as requested via command line...');
      }

      await manager.importFromFile(targetPath);
      console.log('Import successful!');
    } catch (error) {
      if (error instanceof SyntaxError) {
        console.error('Error: Invalid JSON format in file.');
        console.error('Please ensure the file contains valid JSON data.');
      } else {
        console.error('Import failed:', error instanceof Error ? error.message : error);
      }
      if (!providedPath) {
        console.log('You can try again with a different file.');
      }
    }
  }, {
    context: 'credential import',
    exitOnError: false,
    logger
  });
}

async function exportCredentials(manager: SecureCredentialsManager): Promise<void> {
  return withErrorHandling(async () => {
    console.log('\nExport Credentials');
    console.log('==================');

    const credentials = manager.getCredentials();
    if (credentials.length === 0) {
      console.log('No credentials to export.');
      return;
    }

    console.log(`Found ${credentials.length} credential(s) to export.`);
    const defaultPath = path.join(process.cwd(), 'credentials-export.json');
    const filePath = await askQuestion(`Enter export file path [${defaultPath}]: `);
    const confirmed = await askQuestion('Warning: Exported file will contain sensitive data in plain text. Continue? (y/n): ');

    if (confirmed.toLowerCase() !== 'y') {
      console.log('Export cancelled.');
      return;
    }

    try {
      await manager.exportToFile(filePath || defaultPath);
      console.log('Export completed successfully!');
      console.log('Remember to store the exported file securely and delete it when no longer needed.');
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, {
    context: 'credential export',
    exitOnError: false,
    logger
  });
}

main().catch(error => {
  console.error('\nUnexpected error occurred:', error instanceof Error ? error.message : error);
  console.error('The application will now exit. Please try again.');
  rl.close();
});