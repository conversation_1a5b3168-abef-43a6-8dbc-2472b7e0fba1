/**
 * Tests for credential-manager-cli.ts
 */

import {
  parseArguments,
  getHelpMessage,
  importCredentials,
  listCredentials,
  createCredentialFromInput,
  validateCredentialInput,
  parseCredentialSelection,
  formatCredentialsForSelection,
  generateDefaultExportPath,
  validateExportConfirmation,
  formatExportSummary,
  generateDefaultImportPath,
  validateImportFilePath,
  resolveImportFilePath,
  formatImportInstructions,
  getHelpSections,
  formatHelpMessage,
  validateJsonCredentials,
  getMenuOptions,
  parseMenuChoice,
  executeMenuAction,
  cleanupReadline,
  CLIOptions,
  MenuAction
} from '../../src/cli/credential-manager-cli';
import { SecureCredentialsManager } from '../../src/services/secure-credentials-manager';
import * as fs from 'fs/promises';

// Mock dependencies
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger');
jest.mock('../../src/utils/error-handler');
jest.mock('fs/promises');

const mockFs = fs as jest.Mocked<typeof fs>;

describe('credential-manager-cli', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up readline interface after each test
    cleanupReadline();
  });

  describe('parseArguments', () => {
    it('should parse import flag correctly', () => {
      const args = ['--import', 'test.json'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('test.json');
      expect(options.showHelp).toBeUndefined();
    });

    it('should parse short import flag correctly', () => {
      const args = ['-i', 'config.json'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('config.json');
    });

    it('should parse help flag correctly', () => {
      const args = ['--help'];
      const options = parseArguments(args);

      expect(options.showHelp).toBe(true);
      expect(options.importFilePath).toBeUndefined();
    });

    it('should parse short help flag correctly', () => {
      const args = ['-h'];
      const options = parseArguments(args);

      expect(options.showHelp).toBe(true);
    });

    it('should throw error for import flag without file path', () => {
      const args = ['--import'];

      expect(() => parseArguments(args)).toThrow('--import flag requires a file path argument.');
    });

    it('should handle empty arguments', () => {
      const args: string[] = [];
      const options = parseArguments(args);

      expect(options.importFilePath).toBeUndefined();
      expect(options.showHelp).toBeUndefined();
    });

    it('should handle multiple flags', () => {
      const args = ['--import', 'test.json', '--help'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('test.json');
      expect(options.showHelp).toBe(true);
    });

    it('should handle complex argument combinations', () => {
      const args = ['--import', 'file1.json', '--help', '--import', 'file2.json'];
      const options = parseArguments(args);

      // Should take the last import file
      expect(options.importFilePath).toBe('file2.json');
      expect(options.showHelp).toBe(true);
    });

    it('should handle arguments with spaces in file paths', () => {
      const args = ['--import', 'my file with spaces.json'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('my file with spaces.json');
    });

    it('should handle relative and absolute paths', () => {
      const relativePath = './config/credentials.json';
      const absolutePath = '/home/<USER>/credentials.json';

      let options = parseArguments(['--import', relativePath]);
      expect(options.importFilePath).toBe(relativePath);

      options = parseArguments(['--import', absolutePath]);
      expect(options.importFilePath).toBe(absolutePath);
    });
  });

  describe('getHelpMessage', () => {
    it('should return formatted help message', () => {
      const helpMessage = getHelpMessage();

      expect(helpMessage).toContain('Usage: credential-manager-cli [options]');
      expect(helpMessage).toContain('--import, -i <file>');
      expect(helpMessage).toContain('--help, -h');
      expect(helpMessage).toContain('Examples:');
    });

    it('should include examples in help message', () => {
      const helpMessage = getHelpMessage();

      expect(helpMessage).toContain('credential-manager-cli --import');
      expect(helpMessage).toContain('credential-manager-cli -i');
    });
  });

  describe('importCredentials', () => {
    beforeEach(() => {
      // Mock console methods
      jest.spyOn(console, 'log').mockImplementation();
      jest.spyOn(console, 'error').mockImplementation();
    });

    it('should import valid credentials from JSON array', async () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          token: 'test-token',
          puppeteerLogin: false
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await importCredentials('test.json');

      expect(count).toBe(1);
      expect(mockFs.readFile).toHaveBeenCalledWith('test.json', 'utf8');
    });

    it('should import credentials from object with credentials array', async () => {
      const data = {
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            spaceKey: 'TEST'
          }
        ],
        version: '2.0'
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(data));

      const count = await importCredentials('test.json');

      expect(count).toBe(1);
    });

    it('should handle empty credentials array', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify([]));

      await expect(importCredentials('test.json')).rejects.toThrow('JSON array cannot be empty');
    });

    it('should throw error for invalid JSON', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue('invalid json');

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should throw error for file not found', async () => {
      mockFs.access.mockRejectedValue(new Error('ENOENT: no such file or directory'));

      await expect(importCredentials('nonexistent.json')).rejects.toThrow('File not found: nonexistent.json');
    });

    it('should validate credential structure', async () => {
      const invalidCredentials = [
        {
          name: 'Test Confluence'
          // Missing baseUrl
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(invalidCredentials));

      await expect(importCredentials('test.json')).rejects.toThrow('missing or invalid \'baseUrl\' field');
    });

    it('should validate credentials array format', async () => {
      const invalidData = {
        credentials: 'not-an-array'
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(invalidData));

      await expect(importCredentials('test.json')).rejects.toThrow('File must contain an array of credentials or an object with a credentials array.');
    });

    it('should handle credentials with missing optional fields', async () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net'
          // Missing spaceKey - should fail validation
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      await expect(importCredentials('test.json')).rejects.toThrow('missing or invalid \'spaceKey\' field');
    });

    it('should handle file system errors gracefully', async () => {
      mockFs.access.mockRejectedValue(new Error('Permission denied'));

      await expect(importCredentials('test.json')).rejects.toThrow('File not found: test.json');
    });

    it('should handle malformed JSON gracefully', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue('{"incomplete": json}');

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should handle empty files', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue('');

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should handle null/undefined in credentials', async () => {
      const credentials = [
        null,
        {
          name: 'Valid Credential',
          baseUrl: 'https://test.atlassian.net'
        },
        undefined
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid credential at index 0: must be an object');
    });

    it('should handle very large credential files', async () => {
      const largeCredentialArray = Array.from({ length: 1000 }, (_, i) => ({
        name: `Credential ${i}`,
        baseUrl: `https://test${i}.atlassian.net`,
        spaceKey: `TEST${i}`
      }));

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(largeCredentialArray));

      const count = await importCredentials('large.json');

      expect(count).toBe(1000);
    });

    it('should handle credentials with special characters', async () => {
      const credentials = [
        {
          name: 'Test Confluence with émojis 🚀',
          baseUrl: 'https://test-émojis.atlassian.net',
          spaceKey: 'TEST-🚀'
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await importCredentials('special.json');

      expect(count).toBe(1);
    });

    it('should handle nested object structures', async () => {
      const data = {
        metadata: {
          version: '2.0',
          created: '2023-01-01'
        },
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            spaceKey: 'TEST',
            config: {
              timeout: 5000,
              retries: 3
            }
          }
        ]
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(data));

      const count = await importCredentials('nested.json');

      expect(count).toBe(1);
    });
  });

  describe('listCredentials', () => {
    let mockManager: jest.Mocked<SecureCredentialsManager>;

    beforeEach(() => {
      mockManager = {
        getCredentials: jest.fn()
      } as any;
    });

    it('should return message when no credentials exist', () => {
      mockManager.getCredentials.mockReturnValue([]);

      const result = listCredentials(mockManager);

      expect(result).toEqual(['No credentials stored.']);
      expect(mockManager.getCredentials).toHaveBeenCalled();
    });

    it('should format single credential correctly', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: false,
          token: 'test-token'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).toEqual([
        '\nStored Credentials:',
        '1. Test Confluence (https://test.atlassian.net)',
        '   Space Key: TEST',
        '   Auth Method: API Token',
        ''
      ]);
    });

    it('should format multiple credentials correctly', () => {
      const credentials = [
        {
          name: 'Test Confluence 1',
          baseUrl: 'https://test1.atlassian.net',
          spaceKey: 'TEST1',
          puppeteerLogin: false,
          token: 'test-token-1'
        },
        {
          name: 'Test Confluence 2',
          baseUrl: 'https://test2.atlassian.net',
          spaceKey: 'TEST2',
          puppeteerLogin: true,
          username: 'testuser'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).toEqual([
        '\nStored Credentials:',
        '1. Test Confluence 1 (https://test1.atlassian.net)',
        '   Space Key: TEST1',
        '   Auth Method: API Token',
        '',
        '2. Test Confluence 2 (https://test2.atlassian.net)',
        '   Space Key: TEST2',
        '   Auth Method: Browser Login',
        '   Username: testuser',
        ''
      ]);
    });

    it('should handle credentials with username', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: true,
          username: '<EMAIL>'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).toContain('   Username: <EMAIL>');
      expect(result).toContain('   Auth Method: Browser Login');
    });

    it('should handle credentials without username', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: false,
          token: 'test-token'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).not.toContain('Username:');
      expect(result).toContain('   Auth Method: API Token');
    });
  });

  describe('createCredentialFromInput', () => {
    it('should create credential with API token authentication', () => {
      const result = createCredentialFromInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '1',
        'test-token'
      );

      expect(result).toEqual({
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        puppeteerLogin: false,
        token: 'test-token',
        username: undefined,
        password: undefined
      });
    });

    it('should create credential with browser login authentication', () => {
      const result = createCredentialFromInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '2'
      );

      expect(result).toEqual({
        name: 'Test Confluence',
        baseUrl: 'https://test.atlassian.net',
        spaceKey: 'TEST',
        puppeteerLogin: true,
        token: undefined,
        username: undefined,
        password: undefined
      });
    });

    it('should ignore token for browser login', () => {
      const result = createCredentialFromInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '2',
        'ignored-token'
      );

      expect(result.puppeteerLogin).toBe(true);
      expect(result.token).toBeUndefined();
    });
  });

  describe('validateCredentialInput', () => {
    it('should validate correct API token input', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '1',
        'test-token'
      );

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should validate correct browser login input', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '2'
      );

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
    });

    it('should reject empty name', () => {
      const result = validateCredentialInput(
        '',
        'https://test.atlassian.net',
        'TEST',
        '1',
        'test-token'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Name is required');
    });

    it('should reject empty base URL', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        '',
        'TEST',
        '1',
        'test-token'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Base URL is required');
    });

    it('should reject invalid URL', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'invalid-url',
        'TEST',
        '1',
        'test-token'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Base URL must be a valid URL');
    });

    it('should reject empty space key', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'https://test.atlassian.net',
        '',
        '1',
        'test-token'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Space Key is required');
    });

    it('should reject invalid auth method', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '3',
        'test-token'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Authentication Method must be 1 or 2');
    });

    it('should reject missing token for API auth', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '1'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API Token is required for API Token authentication');
    });

    it('should reject empty token for API auth', () => {
      const result = validateCredentialInput(
        'Test Confluence',
        'https://test.atlassian.net',
        'TEST',
        '1',
        ''
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API Token is required for API Token authentication');
    });

    it('should collect multiple validation errors', () => {
      const result = validateCredentialInput(
        '',
        'invalid-url',
        '',
        '3'
      );

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(4);
      expect(result.errors).toContain('Name is required');
      expect(result.errors).toContain('Base URL must be a valid URL');
      expect(result.errors).toContain('Space Key is required');
      expect(result.errors).toContain('Authentication Method must be 1 or 2');
    });
  });

  describe('parseCredentialSelection', () => {
    it('should parse valid selection', () => {
      const result = parseCredentialSelection('2', 5);

      expect(result.isValid).toBe(true);
      expect(result.index).toBe(1);
      expect(result.error).toBeUndefined();
    });

    it('should reject non-numeric input', () => {
      const result = parseCredentialSelection('abc', 5);

      expect(result.isValid).toBe(false);
      expect(result.index).toBe(-1);
      expect(result.error).toBe('Selection must be a number');
    });

    it('should reject selection below range', () => {
      const result = parseCredentialSelection('0', 5);

      expect(result.isValid).toBe(false);
      expect(result.index).toBe(-1);
      expect(result.error).toBe('Selection must be between 1 and 5');
    });

    it('should reject selection above range', () => {
      const result = parseCredentialSelection('6', 5);

      expect(result.isValid).toBe(false);
      expect(result.index).toBe(-1);
      expect(result.error).toBe('Selection must be between 1 and 5');
    });

    it('should handle edge case with single credential', () => {
      const result = parseCredentialSelection('1', 1);

      expect(result.isValid).toBe(true);
      expect(result.index).toBe(0);
    });

    it('should reject negative numbers', () => {
      const result = parseCredentialSelection('-1', 5);

      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Selection must be a number');
    });

    it('should reject decimal numbers', () => {
      const result = parseCredentialSelection('2.5', 5);

      expect(result.isValid).toBe(false);
      expect(result.index).toBe(-1);
      expect(result.error).toBe('Selection must be a number');
    });
  });

  describe('formatCredentialsForSelection', () => {
    it('should format empty credentials list', () => {
      const result = formatCredentialsForSelection([]);

      expect(result).toEqual(['No credentials available.']);
    });

    it('should format single credential', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: false,
          token: 'test-token'
        }
      ];

      const result = formatCredentialsForSelection(credentials as any);

      expect(result).toEqual([
        '\nSelect credential:',
        '1. Test Confluence (https://test.atlassian.net)'
      ]);
    });

    it('should format multiple credentials', () => {
      const credentials = [
        {
          name: 'Test Confluence 1',
          baseUrl: 'https://test1.atlassian.net',
          spaceKey: 'TEST1',
          puppeteerLogin: false,
          token: 'test-token-1'
        },
        {
          name: 'Test Confluence 2',
          baseUrl: 'https://test2.atlassian.net',
          spaceKey: 'TEST2',
          puppeteerLogin: true
        }
      ];

      const result = formatCredentialsForSelection(credentials as any);

      expect(result).toEqual([
        '\nSelect credential:',
        '1. Test Confluence 1 (https://test1.atlassian.net)',
        '2. Test Confluence 2 (https://test2.atlassian.net)'
      ]);
    });

    it('should handle credentials with special characters', () => {
      const credentials = [
        {
          name: 'Test & Company Confluence',
          baseUrl: 'https://test-company.atlassian.net',
          spaceKey: 'T&C',
          puppeteerLogin: false,
          token: 'test-token'
        }
      ];

      const result = formatCredentialsForSelection(credentials as any);

      expect(result).toContain('1. Test & Company Confluence (https://test-company.atlassian.net)');
    });
  });

  describe('generateDefaultExportPath', () => {
    it('should generate correct default export path', () => {
      const result = generateDefaultExportPath();
      expect(result).toContain('credentials-export.json');
      expect(result).toContain(process.cwd());
    });

    it('should use current working directory', () => {
      const result = generateDefaultExportPath();
      const expectedPath = require('path').join(process.cwd(), 'credentials-export.json');
      expect(result).toBe(expectedPath);
    });
  });

  describe('validateExportConfirmation', () => {
    it('should accept "y"', () => {
      expect(validateExportConfirmation('y')).toBe(true);
    });

    it('should accept "Y"', () => {
      expect(validateExportConfirmation('Y')).toBe(true);
    });

    it('should reject "n"', () => {
      expect(validateExportConfirmation('n')).toBe(false);
    });

    it('should reject "N"', () => {
      expect(validateExportConfirmation('N')).toBe(false);
    });

    it('should reject empty string', () => {
      expect(validateExportConfirmation('')).toBe(false);
    });

    it('should reject "yes"', () => {
      expect(validateExportConfirmation('yes')).toBe(false);
    });

    it('should reject "no"', () => {
      expect(validateExportConfirmation('no')).toBe(false);
    });

    it('should reject random text', () => {
      expect(validateExportConfirmation('maybe')).toBe(false);
    });

    it('should handle whitespace', () => {
      expect(validateExportConfirmation(' y ')).toBe(false); // Exact match required
    });
  });

  describe('formatExportSummary', () => {
    it('should format zero credentials', () => {
      const result = formatExportSummary(0);
      expect(result).toBe('No credentials to export.');
    });

    it('should format single credential', () => {
      const result = formatExportSummary(1);
      expect(result).toBe('Found 1 credential(s) to export.');
    });

    it('should format multiple credentials', () => {
      const result = formatExportSummary(5);
      expect(result).toBe('Found 5 credential(s) to export.');
    });

    it('should handle large numbers', () => {
      const result = formatExportSummary(100);
      expect(result).toBe('Found 100 credential(s) to export.');
    });

    it('should handle negative numbers gracefully', () => {
      const result = formatExportSummary(-1);
      expect(result).toBe('Found -1 credential(s) to export.');
    });
  });

  describe('getMenuOptions', () => {
    it('should return correct menu options', () => {
      const result = getMenuOptions();

      expect(result).toEqual([
        '\nOptions:',
        '1. List all credentials',
        '2. Add/Update credential',
        '3. Remove credential',
        '4. Import from JSON file',
        '5. Export credentials to JSON',
        '6. Exit'
      ]);
    });

    it('should return array with 7 items', () => {
      const result = getMenuOptions();
      expect(result).toHaveLength(7);
    });

    it('should include all menu numbers 1-6', () => {
      const result = getMenuOptions();
      const menuText = result.join(' ');

      expect(menuText).toContain('1.');
      expect(menuText).toContain('2.');
      expect(menuText).toContain('3.');
      expect(menuText).toContain('4.');
      expect(menuText).toContain('5.');
      expect(menuText).toContain('6.');
    });
  });

  describe('parseMenuChoice', () => {
    it('should parse valid menu choices', () => {
      expect(parseMenuChoice('1')).toBe('list');
      expect(parseMenuChoice('2')).toBe('add');
      expect(parseMenuChoice('3')).toBe('remove');
      expect(parseMenuChoice('4')).toBe('import');
      expect(parseMenuChoice('5')).toBe('export');
      expect(parseMenuChoice('6')).toBe('exit');
    });

    it('should handle whitespace', () => {
      expect(parseMenuChoice(' 1 ')).toBe('list');
      expect(parseMenuChoice('\t2\t')).toBe('add');
      expect(parseMenuChoice('\n3\n')).toBe('remove');
    });

    it('should return invalid for unknown choices', () => {
      expect(parseMenuChoice('0')).toBe('invalid');
      expect(parseMenuChoice('7')).toBe('invalid');
      expect(parseMenuChoice('abc')).toBe('invalid');
      expect(parseMenuChoice('')).toBe('invalid');
    });

    it('should return invalid for empty string', () => {
      expect(parseMenuChoice('')).toBe('invalid');
    });

    it('should return invalid for non-numeric input', () => {
      expect(parseMenuChoice('list')).toBe('invalid');
      expect(parseMenuChoice('exit')).toBe('invalid');
    });
  });

  describe('executeMenuAction', () => {
    let mockManager: jest.Mocked<SecureCredentialsManager>;
    let consoleSpy: jest.SpyInstance;

    beforeEach(() => {
      mockManager = {
        getCredentials: jest.fn().mockReturnValue([]),
        addOrUpdateCredential: jest.fn(),
        removeCredential: jest.fn(),
        exportToFile: jest.fn(),
        cleanup: jest.fn()
      } as any;

      consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    });

    afterEach(() => {
      consoleSpy.mockRestore();
    });

    it('should handle list action', async () => {
      await executeMenuAction('list', mockManager);
      expect(mockManager.getCredentials).toHaveBeenCalled();
    });

    it('should handle unknown action', async () => {
      await executeMenuAction('invalid' as MenuAction, mockManager);
      expect(consoleSpy).toHaveBeenCalledWith('Unknown action');
    });

    it('should not throw for valid actions', async () => {
      // These will call the actual functions, but they're mocked
      await expect(executeMenuAction('list', mockManager)).resolves.not.toThrow();
    });
  });

  describe('generateDefaultImportPath', () => {
    it('should generate correct default import path', () => {
      const result = generateDefaultImportPath();
      expect(result).toContain('config.json');
      expect(result).toContain(process.cwd());
    });

    it('should use current working directory', () => {
      const result = generateDefaultImportPath();
      const expectedPath = require('path').join(process.cwd(), 'config.json');
      expect(result).toBe(expectedPath);
    });
  });

  describe('validateImportFilePath', () => {
    it('should validate correct JSON file path', () => {
      const result = validateImportFilePath('config.json');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate JSON file with path', () => {
      const result = validateImportFilePath('./data/config.json');
      expect(result.isValid).toBe(true);
    });

    it('should reject empty file path', () => {
      const result = validateImportFilePath('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File path cannot be empty');
    });

    it('should reject whitespace-only file path', () => {
      const result = validateImportFilePath('   ');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File path cannot be empty');
    });

    it('should reject non-JSON files', () => {
      const result = validateImportFilePath('config.txt');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File must be a JSON file (.json)');
    });

    it('should handle case-insensitive JSON extension', () => {
      expect(validateImportFilePath('config.JSON').isValid).toBe(true);
      expect(validateImportFilePath('config.Json').isValid).toBe(true);
    });

    it('should reject files without extension', () => {
      const result = validateImportFilePath('config');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File must be a JSON file (.json)');
    });
  });

  describe('resolveImportFilePath', () => {
    it('should use provided path when available', () => {
      const result = resolveImportFilePath('/path/to/file.json');
      expect(result).toBe('/path/to/file.json');
    });

    it('should use user input when no provided path', () => {
      const result = resolveImportFilePath(undefined, 'user-file.json');
      expect(result).toBe('user-file.json');
    });

    it('should use default path when no input provided', () => {
      const result = resolveImportFilePath(undefined, '');
      expect(result).toContain('config.json');
    });

    it('should use default path when user input is whitespace', () => {
      const result = resolveImportFilePath(undefined, '   ');
      expect(result).toContain('config.json');
    });

    it('should trim user input', () => {
      const result = resolveImportFilePath(undefined, '  custom.json  ');
      expect(result).toBe('custom.json');
    });

    it('should prioritize provided path over user input', () => {
      const result = resolveImportFilePath('/provided/path.json', 'user-input.json');
      expect(result).toBe('/provided/path.json');
    });
  });

  describe('formatImportInstructions', () => {
    it('should format instructions with default path', () => {
      const defaultPath = '/test/config.json';
      const result = formatImportInstructions(defaultPath);

      expect(result).toEqual([
        '\nImport from JSON File',
        '====================',
        'You can import credentials from any JSON file containing an array of credential objects.',
        'Default file: /test/config.json'
      ]);
    });

    it('should return array with 4 items', () => {
      const result = formatImportInstructions('test.json');
      expect(result).toHaveLength(4);
    });

    it('should include the provided default path', () => {
      const customPath = '/custom/path/data.json';
      const result = formatImportInstructions(customPath);
      const instructionsText = result.join(' ');
      expect(instructionsText).toContain(customPath);
    });

    it('should include import title', () => {
      const result = formatImportInstructions('test.json');
      expect(result[0]).toBe('\nImport from JSON File');
      expect(result[1]).toBe('====================');
    });

    it('should include helpful description', () => {
      const result = formatImportInstructions('test.json');
      expect(result[2]).toContain('array of credential objects');
    });
  });

  describe('getHelpSections', () => {
    it('should return help sections with correct structure', () => {
      const result = getHelpSections();

      expect(result).toHaveProperty('usage');
      expect(result).toHaveProperty('options');
      expect(result).toHaveProperty('examples');
    });

    it('should return correct usage string', () => {
      const result = getHelpSections();
      expect(result.usage).toBe('Usage: credential-manager-cli [options]');
    });

    it('should return options array', () => {
      const result = getHelpSections();
      expect(Array.isArray(result.options)).toBe(true);
      expect(result.options.length).toBeGreaterThan(0);
    });

    it('should return examples array', () => {
      const result = getHelpSections();
      expect(Array.isArray(result.examples)).toBe(true);
      expect(result.examples.length).toBeGreaterThan(0);
    });

    it('should include import option', () => {
      const result = getHelpSections();
      const optionsText = result.options.join(' ');
      expect(optionsText).toContain('--import');
      expect(optionsText).toContain('-i');
    });

    it('should include help option', () => {
      const result = getHelpSections();
      const optionsText = result.options.join(' ');
      expect(optionsText).toContain('--help');
      expect(optionsText).toContain('-h');
    });
  });

  describe('formatHelpMessage', () => {
    it('should format help message correctly', () => {
      const sections = {
        usage: 'Usage: test-cli [options]',
        options: ['  --test  Test option'],
        examples: ['  test-cli --test']
      };

      const result = formatHelpMessage(sections);
      const lines = result.split('\n');

      expect(lines[0]).toBe('Usage: test-cli [options]');
      expect(lines[1]).toBe('');
      expect(lines[2]).toBe('Options:');
      expect(lines[3]).toBe('  --test  Test option');
      expect(lines[4]).toBe('');
      expect(lines[5]).toBe('Examples:');
      expect(lines[6]).toBe('  test-cli --test');
    });

    it('should handle multiple options and examples', () => {
      const sections = {
        usage: 'Usage: test',
        options: ['  --opt1  Option 1', '  --opt2  Option 2'],
        examples: ['  test --opt1', '  test --opt2']
      };

      const result = formatHelpMessage(sections);
      expect(result).toContain('--opt1');
      expect(result).toContain('--opt2');
      expect(result).toContain('test --opt1');
      expect(result).toContain('test --opt2');
    });

    it('should handle empty options and examples', () => {
      const sections = {
        usage: 'Usage: test',
        options: [],
        examples: []
      };

      const result = formatHelpMessage(sections);
      expect(result).toContain('Usage: test');
      expect(result).toContain('Options:');
      expect(result).toContain('Examples:');
    });
  });

  describe('validateJsonCredentials', () => {
    it('should validate correct credentials array', () => {
      const credentials = [
        { name: 'Test 1', baseUrl: 'https://test1.com', spaceKey: 'TEST1' },
        { name: 'Test 2', baseUrl: 'https://test2.com', spaceKey: 'TEST2' }
      ];

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(true);
      expect(result.count).toBe(2);
      expect(result.error).toBeUndefined();
    });

    it('should reject non-array input', () => {
      const result = validateJsonCredentials({ not: 'array' });
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('JSON must contain an array of credentials');
    });

    it('should reject empty array', () => {
      const result = validateJsonCredentials([]);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('JSON array cannot be empty');
    });

    it('should reject null/undefined input', () => {
      expect(validateJsonCredentials(null).isValid).toBe(false);
      expect(validateJsonCredentials(undefined).isValid).toBe(false);
    });

    it('should reject non-object credentials', () => {
      const result = validateJsonCredentials(['not an object']);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Invalid credential at index 0: must be an object');
    });

    it('should reject credentials missing name', () => {
      const credentials = [
        { baseUrl: 'https://test.com', spaceKey: 'TEST' }
      ];

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('missing or invalid \'name\' field');
    });

    it('should reject credentials missing baseUrl', () => {
      const credentials = [
        { name: 'Test', spaceKey: 'TEST' }
      ];

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('missing or invalid \'baseUrl\' field');
    });

    it('should reject credentials missing spaceKey', () => {
      const credentials = [
        { name: 'Test', baseUrl: 'https://test.com' }
      ];

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('missing or invalid \'spaceKey\' field');
    });

    it('should reject credentials with invalid field types', () => {
      const credentials = [
        { name: 123, baseUrl: 'https://test.com', spaceKey: 'TEST' }
      ];

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('missing or invalid \'name\' field');
    });

    it('should identify specific index of invalid credential', () => {
      const credentials = [
        { name: 'Valid', baseUrl: 'https://test.com', spaceKey: 'TEST' },
        { name: 'Invalid' } // Missing baseUrl and spaceKey
      ];

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('at index 1');
    });

    it('should handle large arrays', () => {
      const credentials = Array.from({ length: 100 }, (_, i) => ({
        name: `Test ${i}`,
        baseUrl: `https://test${i}.com`,
        spaceKey: `TEST${i}`
      }));

      const result = validateJsonCredentials(credentials);
      expect(result.isValid).toBe(true);
      expect(result.count).toBe(100);
    });
  });
});
