/**
 * Tests for credential-manager-cli.ts
 */

import { parseArguments, getHelpMessage, importCredentials, listCredentials, CLIOptions } from '../../src/cli/credential-manager-cli';
import { SecureCredentialsManager } from '../../src/services/secure-credentials-manager';
import * as fs from 'fs/promises';

// Mock dependencies
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger');
jest.mock('../../src/utils/error-handler');
jest.mock('fs/promises');

const mockFs = fs as jest.Mocked<typeof fs>;

describe('credential-manager-cli', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('parseArguments', () => {
    it('should parse import flag correctly', () => {
      const args = ['--import', 'test.json'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('test.json');
      expect(options.showHelp).toBeUndefined();
    });

    it('should parse short import flag correctly', () => {
      const args = ['-i', 'config.json'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('config.json');
    });

    it('should parse help flag correctly', () => {
      const args = ['--help'];
      const options = parseArguments(args);

      expect(options.showHelp).toBe(true);
      expect(options.importFilePath).toBeUndefined();
    });

    it('should parse short help flag correctly', () => {
      const args = ['-h'];
      const options = parseArguments(args);

      expect(options.showHelp).toBe(true);
    });

    it('should throw error for import flag without file path', () => {
      const args = ['--import'];

      expect(() => parseArguments(args)).toThrow('--import flag requires a file path argument.');
    });

    it('should handle empty arguments', () => {
      const args: string[] = [];
      const options = parseArguments(args);

      expect(options.importFilePath).toBeUndefined();
      expect(options.showHelp).toBeUndefined();
    });

    it('should handle multiple flags', () => {
      const args = ['--import', 'test.json', '--help'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('test.json');
      expect(options.showHelp).toBe(true);
    });

    it('should handle complex argument combinations', () => {
      const args = ['--import', 'file1.json', '--help', '--import', 'file2.json'];
      const options = parseArguments(args);

      // Should take the last import file
      expect(options.importFilePath).toBe('file2.json');
      expect(options.showHelp).toBe(true);
    });

    it('should handle arguments with spaces in file paths', () => {
      const args = ['--import', 'my file with spaces.json'];
      const options = parseArguments(args);

      expect(options.importFilePath).toBe('my file with spaces.json');
    });

    it('should handle relative and absolute paths', () => {
      const relativePath = './config/credentials.json';
      const absolutePath = '/home/<USER>/credentials.json';

      let options = parseArguments(['--import', relativePath]);
      expect(options.importFilePath).toBe(relativePath);

      options = parseArguments(['--import', absolutePath]);
      expect(options.importFilePath).toBe(absolutePath);
    });
  });

  describe('getHelpMessage', () => {
    it('should return formatted help message', () => {
      const helpMessage = getHelpMessage();

      expect(helpMessage).toContain('Usage: credential-manager-cli [options]');
      expect(helpMessage).toContain('--import, -i <file>');
      expect(helpMessage).toContain('--help, -h');
      expect(helpMessage).toContain('Examples:');
    });

    it('should include examples in help message', () => {
      const helpMessage = getHelpMessage();

      expect(helpMessage).toContain('credential-manager-cli --import');
      expect(helpMessage).toContain('credential-manager-cli -i');
    });
  });

  describe('importCredentials', () => {
    beforeEach(() => {
      // Mock console methods
      jest.spyOn(console, 'log').mockImplementation();
      jest.spyOn(console, 'error').mockImplementation();
    });

    it('should import valid credentials from JSON array', async () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          token: 'test-token',
          puppeteerLogin: false
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await importCredentials('test.json');

      expect(count).toBe(1);
      expect(mockFs.readFile).toHaveBeenCalledWith('test.json', 'utf8');
    });

    it('should import credentials from object with credentials array', async () => {
      const data = {
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            spaceKey: 'TEST'
          }
        ],
        version: '2.0'
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(data));

      const count = await importCredentials('test.json');

      expect(count).toBe(1);
    });

    it('should handle empty credentials array', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify([]));

      const count = await importCredentials('test.json');

      expect(count).toBe(0);
    });

    it('should throw error for invalid JSON', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue('invalid json');

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should throw error for file not found', async () => {
      mockFs.access.mockRejectedValue(new Error('ENOENT: no such file or directory'));

      await expect(importCredentials('nonexistent.json')).rejects.toThrow('File not found: nonexistent.json');
    });

    it('should validate credential structure', async () => {
      const invalidCredentials = [
        {
          name: 'Test Confluence'
          // Missing baseUrl
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(invalidCredentials));

      await expect(importCredentials('test.json')).rejects.toThrow('Each credential must have at least name and baseUrl properties.');
    });

    it('should validate credentials array format', async () => {
      const invalidData = {
        credentials: 'not-an-array'
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(invalidData));

      await expect(importCredentials('test.json')).rejects.toThrow('File must contain an array of credentials or an object with a credentials array.');
    });

    it('should handle credentials with missing optional fields', async () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net'
          // Missing optional fields like spaceKey, token
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await importCredentials('test.json');

      expect(count).toBe(1);
    });

    it('should handle file system errors gracefully', async () => {
      mockFs.access.mockRejectedValue(new Error('Permission denied'));

      await expect(importCredentials('test.json')).rejects.toThrow('File not found: test.json');
    });

    it('should handle malformed JSON gracefully', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue('{"incomplete": json}');

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should handle empty files', async () => {
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue('');

      await expect(importCredentials('test.json')).rejects.toThrow('Invalid JSON format in file.');
    });

    it('should handle null/undefined in credentials', async () => {
      const credentials = [
        null,
        {
          name: 'Valid Credential',
          baseUrl: 'https://test.atlassian.net'
        },
        undefined
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      await expect(importCredentials('test.json')).rejects.toThrow('Each credential must have at least name and baseUrl properties.');
    });

    it('should handle very large credential files', async () => {
      const largeCredentialArray = Array.from({ length: 1000 }, (_, i) => ({
        name: `Credential ${i}`,
        baseUrl: `https://test${i}.atlassian.net`,
        spaceKey: `TEST${i}`
      }));

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(largeCredentialArray));

      const count = await importCredentials('large.json');

      expect(count).toBe(1000);
    });

    it('should handle credentials with special characters', async () => {
      const credentials = [
        {
          name: 'Test Confluence with émojis 🚀',
          baseUrl: 'https://test-émojis.atlassian.net',
          spaceKey: 'TEST-🚀'
        }
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(credentials));

      const count = await importCredentials('special.json');

      expect(count).toBe(1);
    });

    it('should handle nested object structures', async () => {
      const data = {
        metadata: {
          version: '2.0',
          created: '2023-01-01'
        },
        credentials: [
          {
            name: 'Test Confluence',
            baseUrl: 'https://test.atlassian.net',
            config: {
              timeout: 5000,
              retries: 3
            }
          }
        ]
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readFile.mockResolvedValue(JSON.stringify(data));

      const count = await importCredentials('nested.json');

      expect(count).toBe(1);
    });
  });

  describe('listCredentials', () => {
    let mockManager: jest.Mocked<SecureCredentialsManager>;

    beforeEach(() => {
      mockManager = {
        getCredentials: jest.fn()
      } as any;
    });

    it('should return message when no credentials exist', () => {
      mockManager.getCredentials.mockReturnValue([]);

      const result = listCredentials(mockManager);

      expect(result).toEqual(['No credentials stored.']);
      expect(mockManager.getCredentials).toHaveBeenCalled();
    });

    it('should format single credential correctly', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: false,
          token: 'test-token'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).toEqual([
        '\nStored Credentials:',
        '1. Test Confluence (https://test.atlassian.net)',
        '   Space Key: TEST',
        '   Auth Method: API Token',
        ''
      ]);
    });

    it('should format multiple credentials correctly', () => {
      const credentials = [
        {
          name: 'Test Confluence 1',
          baseUrl: 'https://test1.atlassian.net',
          spaceKey: 'TEST1',
          puppeteerLogin: false,
          token: 'test-token-1'
        },
        {
          name: 'Test Confluence 2',
          baseUrl: 'https://test2.atlassian.net',
          spaceKey: 'TEST2',
          puppeteerLogin: true,
          username: 'testuser'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).toEqual([
        '\nStored Credentials:',
        '1. Test Confluence 1 (https://test1.atlassian.net)',
        '   Space Key: TEST1',
        '   Auth Method: API Token',
        '',
        '2. Test Confluence 2 (https://test2.atlassian.net)',
        '   Space Key: TEST2',
        '   Auth Method: Browser Login',
        '   Username: testuser',
        ''
      ]);
    });

    it('should handle credentials with username', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: true,
          username: '<EMAIL>'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).toContain('   Username: <EMAIL>');
      expect(result).toContain('   Auth Method: Browser Login');
    });

    it('should handle credentials without username', () => {
      const credentials = [
        {
          name: 'Test Confluence',
          baseUrl: 'https://test.atlassian.net',
          spaceKey: 'TEST',
          puppeteerLogin: false,
          token: 'test-token'
        }
      ];
      mockManager.getCredentials.mockReturnValue(credentials as any);

      const result = listCredentials(mockManager);

      expect(result).not.toContain('Username:');
      expect(result).toContain('   Auth Method: API Token');
    });
  });
});
