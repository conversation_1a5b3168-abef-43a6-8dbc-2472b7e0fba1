#!/usr/bin/env node
/**
 * Optimized Confluence page update CLI with lazy loading and performance improvements
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as readline from 'readline';
import { marked, Renderer } from 'marked';
import { createConfluenceHttpClient } from '../services/http-client';

// Import optimized services and utilities
import { SecureCredentialsManager } from '../services/secure-credentials-manager';
import { SecureCookieManager } from '../services/secure-cookie-manager';
import { lazyPuppeteerManager } from '../services/lazy-puppeteer-manager';
import { logger } from '../utils/logger';
import { askPassword } from '../utils/password-input';
import { PerformanceUtils, setupProcessCleanup } from '../utils/performance-utils';
import { FileUtils } from '../utils/file-utils';
import { withErrorHandling } from '../utils/error-handler';
import { NetworkError, ValidationError } from '../errors';

// Setup process cleanup handlers
setupProcessCleanup();

// Initialize managers with lazy loading
const credentialsManager = new SecureCredentialsManager();
const cookieManager = new SecureCookieManager();

export interface CliArguments {
  pageId?: string;
  filePath?: string;
  instanceIdentifier?: string;
}

/**
 * Parse CLI arguments (testable function)
 */
export function parseCliArguments(argv: string[]): CliArguments {
  const pageId = argv[2];
  const filePath = argv[3];
  let instanceIdentifier = argv.slice(4).join(' ').trim();

  // Clean up PowerShell special characters
  instanceIdentifier = instanceIdentifier.replace(/\^/g, '');

  return {
    pageId,
    filePath,
    instanceIdentifier: instanceIdentifier || undefined
  };
}

/**
 * Validate page ID format (testable function)
 */
export function validatePageId(pageId: string): { isValid: boolean; error?: string } {
  if (!pageId || pageId.trim().length === 0) {
    return { isValid: false, error: 'Page ID cannot be empty' };
  }

  if (!/^\d+$/.test(pageId.trim())) {
    return { isValid: false, error: 'Page ID must be a number' };
  }

  return { isValid: true };
}

/**
 * Validate file path (testable function)
 */
export function validateFilePath(filePath: string): { isValid: boolean; error?: string } {
  if (!filePath || filePath.trim().length === 0) {
    return { isValid: false, error: 'File path cannot be empty' };
  }

  if (!filePath.toLowerCase().endsWith('.md') && !filePath.toLowerCase().endsWith('.markdown')) {
    return { isValid: false, error: 'File must be a Markdown file (.md or .markdown)' };
  }

  return { isValid: true };
}

/**
 * Find credential by identifier (testable function)
 */
export function findCredentialByIdentifier(
  credentials: any[],
  identifier: string
): { credential?: any; error?: string } {
  if (!identifier || identifier.trim().length === 0) {
    return { error: 'Instance identifier cannot be empty' };
  }

  if (credentials.length === 0) {
    return { error: 'No credentials available' };
  }

  // Try to find credential by name first (case-insensitive)
  let credential = credentials.find(c =>
    c.name.toLowerCase() === identifier.toLowerCase()
  );

  // If not found by name, try by baseUrl
  if (!credential) {
    credential = credentials.find(c => c.baseUrl === identifier);
  }

  if (!credential) {
    return {
      error: `No credentials found for "${identifier}". Available instances: ${credentials.map(c => c.name).join(', ')}`
    };
  }

  return { credential };
}

/**
 * Process HTML content for Confluence (testable function)
 */
export function processHtmlForConfluence(htmlContent: string): string {
  return htmlContent
    .replace(/<br(?!\s*\/)>/g, '<br />')
    .replace(/<hr(?!\s*\/)>/g, '<hr />')
    .replace(/<img([^>]*?)(?<!\s\/)>/g, '<img$1 />')
    .replace(/<input([^>]*?)(?<!\s\/)>/g, '<input$1 />');
}

export interface InitializationResult {
  success: boolean;
  cancelled?: boolean;
  masterPassword?: string;
}

/**
 * Initialize credentials manager with retry mechanism (testable function)
 */
export async function initializeCredentialsManager(
  credentialsManager: SecureCredentialsManager
): Promise<InitializationResult> {
  let initialized = false;
  let attempts = 0;
  const maxAttempts = 3;
  let masterPassword: string | undefined;

  while (!initialized && attempts < maxAttempts) {
    attempts++;
    try {
      await credentialsManager.initialize();
      initialized = true;
    } catch (error: any) {
      if (error.message === 'Password required to decrypt master key') {
        const { askPassword } = await import('../utils/password-input');
        masterPassword = await askPassword(`Enter master password${attempts > 1 ? ` (attempt ${attempts}/${maxAttempts})` : ''}: `);

        // Handle Ctrl+C (empty password returned)
        if (masterPassword === '') {
          return { success: false, cancelled: true };
        }

        try {
          await credentialsManager.initialize(masterPassword);
          initialized = true;
        } catch (authError: any) {
          if (attempts < maxAttempts) {
            console.log('This could be due to:');
            console.log('- Incorrect password');
            console.log('- Missing or corrupted key files');
            console.log('- File permission issues');
            console.log('Please try again...\n');
          }
        }
      } else {
        throw error;
      }
    }
  }

  return {
    success: initialized,
    cancelled: false,
    masterPassword
  };
}

function askQuestion(query: string): Promise<string> {
    return new Promise(resolve => {
        // Ensure stdin is in the correct state
        if (process.stdin.isTTY) {
            process.stdin.setRawMode(false);
        }

        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout,
            terminal: true
        });

        rl.question(query, (answer) => {
            rl.close();
            resolve(answer.trim());
        });

        // Handle potential readline errors
        rl.on('error', (err) => {
            logger.error('Readline error:', err);
            rl.close();
            resolve('');
        });
    });
}

/**
 * Ensure authenticated session using lazy-loaded Puppeteer
 */
async function ensureAuthenticatedSession(confluenceBaseUrl: string, masterPassword?: string): Promise<void> {
    return withErrorHandling(async () => {
        const forceLogin = process.env.FORCE_CONFLUENCE_LOGIN === 'true';
        let needsLogin = false;
        let validCookies = null;

        // Check for existing cookies without validation if not forcing login
        if (!forceLogin) {
            try {
                validCookies = cookieManager.getCookiesForBaseUrl(confluenceBaseUrl);
                needsLogin = !validCookies || validCookies.length === 0;
                if (validCookies && validCookies.length > 0) {
                    logger.info('Encrypted cookies found and loaded.');
                }
            } catch (error) {
                needsLogin = true;
                logger.info('No valid cookies found. Manual login required.');
            }
        } else {
            logger.info('FORCE_CONFLUENCE_LOGIN=true: Manual login enforced.');
            needsLogin = true;
        }

        // Use existing cookies without validation if available
        if (!needsLogin && validCookies && validCookies.length > 0) {
            logger.info('Using existing cookies without validation.');
            return;
        }

        // Only launch Puppeteer if login is needed
        logger.info('Launching browser for manual login...');
        PerformanceUtils.logMemoryUsage('before-puppeteer-session');

        const browser = await lazyPuppeteerManager.launchBrowser();
        const page = await lazyPuppeteerManager.createPage(browser);

        try {
            logger.info(`Navigating to ${confluenceBaseUrl} for manual login...`);
            await lazyPuppeteerManager.navigateToUrl(page, confluenceBaseUrl);

            logger.info('Please log in manually in the browser. The script is waiting...');

            // Wait for successful login - UI validation only
            let loginSuccess = false;
            const maxWaitTime = 300000; // 5 minutes
            const startTime = Date.now();

            while (!loginSuccess && (Date.now() - startTime) < maxWaitTime) {
                try {
                    // Try API validation first, fall back to UI validation
                    loginSuccess = await lazyPuppeteerManager.validateSessionViaAPI(page, confluenceBaseUrl);

                    if (!loginSuccess) {
                        // Short pause between attempts
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                } catch (error) {
                    logger.debug('Waiting for login...');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            if (!loginSuccess) {
                throw new Error('Login timeout reached. Please try again.');
            }

            logger.info('Manual login successfully detected!');

            // Save new cookies with encryption
            const cookies = await lazyPuppeteerManager.getCookies(page);
            await cookieManager.saveCookies(cookies, confluenceBaseUrl);

            PerformanceUtils.logMemoryUsage('after-puppeteer-session');
        } finally {
            // Always cleanup browser resources
            await lazyPuppeteerManager.closePage(page);
            await lazyPuppeteerManager.closeBrowser(browser);
        }
    }, {
        context: 'authenticated session setup',
        exitOnError: true,
        logger
    });
}

/**
 * Make API call with 401 retry logic
 */
async function makeApiCall(
    url: string,
    options: any,
    confluenceBaseUrl: string,
    masterPassword?: string,
    retryOnAuth: boolean = true
): Promise<any> {
    return withErrorHandling(async () => {
        // Create HTTP client with unified User-Agent
        const httpClient = createConfluenceHttpClient(confluenceBaseUrl);

        try {
            return await httpClient(url, options);
        } catch (error: any) {
            if (error.response?.status === 401 && retryOnAuth) {
                logger.info('🔒 401 Unauthorized - Cookies are invalid, performing new login...');

                // Clear invalid cookies from secure storage
                try {
                    await cookieManager.clearCookiesForBaseUrl(confluenceBaseUrl);
                    logger.info('Invalid encrypted cookies deleted.');
                } catch (e) {
                    logger.warn('Warning: Error deleting encrypted cookies:', e);
                }

                // Perform new login
                await ensureAuthenticatedSession(confluenceBaseUrl, masterPassword);
                logger.info('New login successful.');

                // Load new cookies
                const cookieArray = cookieManager.getCookiesForBaseUrl(confluenceBaseUrl);
                if (cookieArray.length > 0) {
                    const sessionCookie = cookieArray.map((c) => `${c.name}=${c.value}`).join('; ');
                    logger.info(`${cookieArray.length} new encrypted cookies loaded.`);

                    // Update headers with new cookies
                    if (options.headers) {
                        options.headers['Cookie'] = sessionCookie;
                    }

                    // Retry the API call with new cookies (without further retry logic)
                    return await httpClient(url, { ...options, headers: { ...options.headers, 'Cookie': sessionCookie } });
                } else {
                    throw new NetworkError(
                        'Could not retrieve session cookies after re-authentication',
                        'Fatal: Could not retrieve session cookies even after a successful re-authentication.'
                    );
                }
            }
            throw error;
        }
    }, {
        context: 'API call',
        exitOnError: false,
        logger
    });
}

async function main(): Promise<void> {
    try {
        PerformanceUtils.logMemoryUsage('application-start');

        // Initialize credentials manager with retry mechanism
        const initResult = await initializeCredentialsManager(credentialsManager);
        if (!initResult.success) {
            if (initResult.cancelled) {
                logger.info('Operation cancelled by user.');
            } else {
                logger.error('Failed to initialize credentials manager after maximum attempts.');
            }
            return;
        }

        const masterPassword = initResult.masterPassword;

        // Initialize cookie manager with the same password
        try {
            await cookieManager.initialize(masterPassword);
            logger.info('Cookie manager initialized with encryption support.');
        } catch (cookieError: any) {
            logger.warn(`Warning: Cookie manager initialization failed: ${cookieError.message}`);
            logger.warn('Cookies will be handled in legacy mode if needed.');
        }

        // Small delay to ensure stdin is properly reset after password input
        await new Promise(resolve => setTimeout(resolve, 100));

        // Get command line arguments
        const parsedArgs = parseCliArguments(process.argv);
        let confluencePageId = parsedArgs.pageId;
        let markdownFilePath = parsedArgs.filePath;
        let confluenceInstanceIdentifier = parsedArgs.instanceIdentifier;

        // Get credentials
        const credentials = credentialsManager.getCredentials();
        logger.debug(`Loaded ${credentials.length} credentials from secure store`);

        let cred;
        if (!confluenceInstanceIdentifier) {
            if (credentials.length === 0) {
                logger.error('❌ No credentials found. Please set up credentials first using credential-manager-cli.');
                return;
            }

            logger.info('Available Confluence instances:');
            credentials.forEach((cred, idx) => {
                logger.info(`${idx + 1}: ${cred.name}`);
            });

            const prompt = `Please select Confluence instance [1-${credentials.length}]: `;
            const envChoice = await askQuestion(prompt);
            const choiceIdx = parseInt(envChoice.trim(), 10);
            cred = credentials[isNaN(choiceIdx) || choiceIdx < 1 || choiceIdx > credentials.length ? 0 : choiceIdx - 1];
        } else {
            // Try to find credential by name first, then by baseUrl
            cred = credentials.find(c => c.name.toLowerCase() === confluenceInstanceIdentifier.toLowerCase());
            if (!cred) {
                cred = credentialsManager.getCredentialByBaseUrl(confluenceInstanceIdentifier);
            }
            if (!cred) {
                logger.error(`❌ No credentials found for "${confluenceInstanceIdentifier}".`);
                logger.info('Available instances:');
                credentials.forEach((c) => {
                    logger.info(`  - ${c.name} (${c.baseUrl})`);
                });
                return;
            }
        }

        const confluenceBaseUrl = cred.baseUrl;
        logger.info(`Using Confluence instance: ${cred.name} (${confluenceBaseUrl})`);

        let apiToken = cred.token;
        let sessionCookie: string | undefined;
        const spaceKey = cred.spaceKey;
        const puppeteerLogin = cred.puppeteerLogin;

        if (!spaceKey) {
            logger.error(`❌ No spaceKey found for ${confluenceBaseUrl} in configuration.`);
            return;
        }

        // Handle authentication
        if (puppeteerLogin) {
            logger.info('Ensuring a valid Confluence session via Puppeteer...');
            try {
                await ensureAuthenticatedSession(confluenceBaseUrl, masterPassword);
                logger.info('Successfully authenticated via Puppeteer.');

                // Load cookies
                const cookieArray = cookieManager.getCookiesForBaseUrl(confluenceBaseUrl);
                if (cookieArray.length > 0) {
                    sessionCookie = cookieArray.map((c) => `${c.name}=${c.value}`).join('; ');
                    logger.info(`${cookieArray.length} cookies loaded for API requests.`);
                    apiToken = undefined; // Use session cookie instead of API token
                } else {
                    logger.info('No valid session cookies found. Proceeding without session cookie.');
                }
            } catch (error: any) {
                logger.error('❌ Error during Puppeteer login:', error.message);
                return;
            }
        } else if (!apiToken) {
            logger.error(`❌ No API token found for ${confluenceBaseUrl}. Please use credentials manager.`);
            return;
        }

        // Get page ID and markdown file
        if (!confluencePageId) {
            confluencePageId = await askQuestion("Please enter the Confluence Page ID: ");
        }
        if (!confluencePageId) {
            throw new ValidationError('Page ID cannot be empty', 'Page ID cannot be empty.');
        }

        if (!markdownFilePath) {
            markdownFilePath = await askQuestion("Please enter the path to the Markdown file: ");
        }
        if (!markdownFilePath || !(await FileUtils.fileExists(markdownFilePath))) {
            throw new ValidationError(`File not found: ${markdownFilePath}`, `File not found at: ${markdownFilePath}`);
        }

        // Read and process markdown content
        logger.info('Processing markdown content...');
        const mdContent = await FileUtils.readFile(markdownFilePath);

        // Configure markdown renderer
        const renderer = new Renderer();
        renderer.code = function({ text, lang }) {
            const actualLanguage = lang ? lang.toLowerCase() : 'plain';
            const safeCode = text.replace(/]]>/g, ']]>');
            let macro = '<ac:structured-macro ac:name="code" ac:schema-version="1">\n';
            macro += `  <ac:parameter ac:name="language">${actualLanguage}</ac:parameter>\n`;
            macro += '  <ac:plain-text-body><![CDATA[';
            macro += safeCode;
            macro += ']]></ac:plain-text-body>\n';
            macro += '</ac:structured-macro>';
            return macro;
        };

        marked.setOptions({
            renderer: renderer,
            breaks: true
        });

        let htmlContent = marked.parse(mdContent) as string;
        htmlContent = processHtmlForConfluence(htmlContent);

        // Fetch current page content
        logger.info(`Fetching current page content for ID: ${confluencePageId}`);
        const getPageUrl = `${confluenceBaseUrl}/rest/api/content/${confluencePageId}?expand=body.storage,version`;
        const getHeaders = sessionCookie ?
            { 'Cookie': sessionCookie, 'Content-Type': 'application/json' } :
            { 'Authorization': `Bearer ${apiToken}`, 'Content-Type': 'application/json' };

        const getResponse = await makeApiCall(getPageUrl, { method: 'GET', headers: getHeaders }, confluenceBaseUrl, masterPassword);

        if (getResponse.status !== 200) {
            throw new NetworkError(
                `HTTP error! status: ${getResponse.status}`,
                `HTTP error! status: ${getResponse.status} - ${JSON.stringify(getResponse.data)}`
            );
        }

        const pageData = getResponse.data;
        const currentVersion = pageData.version.number;
        logger.info(`Current page version: ${currentVersion}`);

        // Update page content
        const updatePageUrl = `${confluenceBaseUrl}/rest/api/content/${confluencePageId}`;
        const updateData = {
            id: confluencePageId,
            type: 'page',
            title: pageData.title,
            version: { number: currentVersion + 1 },
            body: {
                storage: {
                    value: htmlContent,
                    representation: 'storage'
                }
            }
        };

        const updateHeaders = sessionCookie ?
            { 'Cookie': sessionCookie, 'Content-Type': 'application/json', 'X-Atlassian-Token': 'no-check' } :
            { 'Authorization': `Bearer ${apiToken}`, 'Content-Type': 'application/json' };

        logger.info(`Updating page ID: ${confluencePageId} to version ${currentVersion + 1}`);
        const updateResponse = await makeApiCall(updatePageUrl, {
            method: 'PUT',
            headers: updateHeaders,
            data: updateData
        }, confluenceBaseUrl, masterPassword);

        if (updateResponse.status !== 200) {
            throw new NetworkError(
                `HTTP error! status: ${updateResponse.status}`,
                `HTTP error! status: ${updateResponse.status} - ${JSON.stringify(updateResponse.data)}`
            );
        }

        logger.info('✅ Confluence page updated successfully!');
        logger.info(`Link: ${confluenceBaseUrl}/pages/viewpage.action?pageId=${updateResponse.data.id}`);

        PerformanceUtils.logMemoryUsage('application-end');
    } catch (err: any) {
        logger.error(`❌ An error occurred: ${err.message}`);
    } finally {
        // Cleanup will be handled by process cleanup handlers
    }
}

// Run the main function
main().catch((error) => {
    logger.error('Unhandled error in main:', error);
    process.exit(1);
});