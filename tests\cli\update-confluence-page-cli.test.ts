/**
 * Tests for update-confluence-page-cli.ts
 */

import {
  parseCliArguments,
  validatePageId,
  validateFilePath,
  findCredentialByIdentifier,
  processHtmlForConfluence,
  initializeCredentialsManager,
  askQuestion,
  isForceLoginEnabled,
  formatInitializationErrorMessage,
  formatPasswordPrompt,
  areCookiesValid,
  formatCredentialsList,
  isLoginNeeded,
  formatLoginTimeoutError,
  formatLoginSuccessMessage,
  formatLoginWaitingMessage,
  formatBrowserLaunchMessage,
  formatManualLoginInstruction,
  calculateRemainingWaitTime,
  isWaitTimeExceeded,
  CliArguments,
  InitializationResult
} from '../../src/cli/update-confluence-page-cli';

// Mock dependencies
jest.mock('fs/promises');
jest.mock('marked');
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/services/secure-cookie-manager');
jest.mock('../../src/services/lazy-puppeteer-manager');
jest.mock('../../src/services/http-client');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger');
jest.mock('../../src/utils/performance-utils');

describe('update-confluence-page-cli', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('parseCliArguments', () => {
    it('should parse complete arguments', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', 'Test Confluence Instance'];
      const result = parseCliArguments(argv);

      expect(result).toEqual({
        pageId: '12345',
        filePath: 'test.md',
        instanceIdentifier: 'Test Confluence Instance'
      });
    });

    it('should handle missing arguments', () => {
      const argv = ['node', 'script.js'];
      const result = parseCliArguments(argv);

      expect(result).toEqual({
        pageId: undefined,
        filePath: undefined,
        instanceIdentifier: undefined
      });
    });

    it('should handle partial arguments', () => {
      const argv = ['node', 'script.js', '12345'];
      const result = parseCliArguments(argv);

      expect(result).toEqual({
        pageId: '12345',
        filePath: undefined,
        instanceIdentifier: undefined
      });
    });

    it('should clean PowerShell special characters', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', 'Test^Confluence^Instance'];
      const result = parseCliArguments(argv);

      expect(result.instanceIdentifier).toBe('TestConfluenceInstance');
    });

    it('should handle multi-word instance identifiers', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', 'My', 'Company', 'Confluence'];
      const result = parseCliArguments(argv);

      expect(result.instanceIdentifier).toBe('My Company Confluence');
    });

    it('should handle empty instance identifier', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', ''];
      const result = parseCliArguments(argv);

      expect(result.instanceIdentifier).toBeUndefined();
    });
  });

  describe('validatePageId', () => {
    it('should validate correct page ID', () => {
      const result = validatePageId('12345');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject empty page ID', () => {
      const result = validatePageId('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID cannot be empty');
    });

    it('should reject non-numeric page ID', () => {
      const result = validatePageId('invalid-id');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID must be a number');
    });

    it('should reject page ID with letters', () => {
      const result = validatePageId('123abc');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID must be a number');
    });

    it('should handle whitespace-only page ID', () => {
      const result = validatePageId('   ');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID cannot be empty');
    });

    it('should validate page ID with leading/trailing spaces', () => {
      const result = validatePageId('  12345  ');
      expect(result.isValid).toBe(true);
    });
  });

  describe('validateFilePath', () => {
    it('should validate .md file', () => {
      const result = validateFilePath('test.md');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate .markdown file', () => {
      const result = validateFilePath('test.markdown');
      expect(result.isValid).toBe(true);
    });

    it('should reject empty file path', () => {
      const result = validateFilePath('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File path cannot be empty');
    });

    it('should reject non-markdown files', () => {
      const result = validateFilePath('test.txt');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File must be a Markdown file (.md or .markdown)');
    });

    it('should handle case-insensitive extensions', () => {
      expect(validateFilePath('test.MD').isValid).toBe(true);
      expect(validateFilePath('test.MARKDOWN').isValid).toBe(true);
    });

    it('should handle paths with directories', () => {
      const result = validateFilePath('./docs/readme.md');
      expect(result.isValid).toBe(true);
    });
  });

  describe('findCredentialByIdentifier', () => {
    const mockCredentials = [
      { name: 'Test Confluence', baseUrl: 'https://test.atlassian.net' },
      { name: 'Production Confluence', baseUrl: 'https://prod.atlassian.net' },
      { name: 'Dev Environment', baseUrl: 'https://dev.atlassian.net' }
    ];

    it('should find credential by exact name', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'Test Confluence');
      expect(result.credential).toEqual(mockCredentials[0]);
      expect(result.error).toBeUndefined();
    });

    it('should find credential by case-insensitive name', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'test confluence');
      expect(result.credential).toEqual(mockCredentials[0]);
    });

    it('should find credential by base URL', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'https://prod.atlassian.net');
      expect(result.credential).toEqual(mockCredentials[1]);
    });

    it('should return error for non-existent identifier', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'Non-existent');
      expect(result.credential).toBeUndefined();
      expect(result.error).toContain('No credentials found for "Non-existent"');
      expect(result.error).toContain('Available instances: Test Confluence, Production Confluence, Dev Environment');
    });

    it('should return error for empty identifier', () => {
      const result = findCredentialByIdentifier(mockCredentials, '');
      expect(result.error).toBe('Instance identifier cannot be empty');
    });

    it('should return error for empty credentials array', () => {
      const result = findCredentialByIdentifier([], 'Test');
      expect(result.error).toBe('No credentials available');
    });

    it('should handle whitespace in identifier', () => {
      const result = findCredentialByIdentifier(mockCredentials, '  Test Confluence  ');
      expect(result.credential).toBeUndefined(); // Exact match required
    });
  });

  describe('processHtmlForConfluence', () => {
    it('should convert self-closing br tags', () => {
      const html = '<p>Test</p><br><p>More text</p>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Test</p><br /><p>More text</p>');
    });

    it('should convert self-closing hr tags', () => {
      const html = '<p>Before</p><hr><p>After</p>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Before</p><hr /><p>After</p>');
    });

    it('should convert img tags', () => {
      const html = '<img src="test.jpg" alt="Test">';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<img src="test.jpg" alt="Test" />');
    });

    it('should convert input tags', () => {
      const html = '<input type="text" name="test">';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<input type="text" name="test" />');
    });

    it('should preserve already closed tags', () => {
      const html = '<p>Test</p><br /><hr /><img src="test.jpg" />';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Test</p><br /><hr /><img src="test.jpg" />');
    });

    it('should handle complex HTML', () => {
      const html = '<div><p>Test</p><br><img src="image.png"><hr><input type="submit"></div>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<div><p>Test</p><br /><img src="image.png" /><hr /><input type="submit" /></div>');
    });

    it('should handle empty string', () => {
      const result = processHtmlForConfluence('');
      expect(result).toBe('');
    });

    it('should handle HTML without self-closing tags', () => {
      const html = '<p>Just a paragraph</p><div>And a div</div>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Just a paragraph</p><div>And a div</div>');
    });
  });

  describe('initializeCredentialsManager', () => {
    let mockCredentialsManager: jest.Mocked<any>;

    beforeEach(() => {
      mockCredentialsManager = {
        initialize: jest.fn()
      };
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should initialize successfully without password', async () => {
      mockCredentialsManager.initialize.mockResolvedValue(undefined);

      const result = await initializeCredentialsManager(mockCredentialsManager);

      expect(result.success).toBe(true);
      expect(result.cancelled).toBe(false);
      expect(result.masterPassword).toBeUndefined();
      expect(mockCredentialsManager.initialize).toHaveBeenCalledTimes(1);
    });

    it('should handle non-password errors', async () => {
      mockCredentialsManager.initialize.mockRejectedValue(new Error('File system error'));

      await expect(initializeCredentialsManager(mockCredentialsManager))
        .rejects.toThrow('File system error');
    });

    it('should return correct structure for success', async () => {
      mockCredentialsManager.initialize.mockResolvedValue(undefined);

      const result = await initializeCredentialsManager(mockCredentialsManager);

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('cancelled');
      expect(result).toHaveProperty('masterPassword');
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.cancelled).toBe('boolean');
    });
  });

  describe('askQuestion', () => {
    it('should be a function', () => {
      expect(typeof askQuestion).toBe('function');
    });

    it('should be callable', () => {
      expect(askQuestion).toBeDefined();
      // We can't easily test the full readline functionality in unit tests
    });
  });

  describe('isForceLoginEnabled', () => {
    let originalEnv: string | undefined;

    beforeEach(() => {
      originalEnv = process.env.FORCE_CONFLUENCE_LOGIN;
    });

    afterEach(() => {
      if (originalEnv !== undefined) {
        process.env.FORCE_CONFLUENCE_LOGIN = originalEnv;
      } else {
        delete process.env.FORCE_CONFLUENCE_LOGIN;
      }
    });

    it('should return true when FORCE_CONFLUENCE_LOGIN is "true"', () => {
      process.env.FORCE_CONFLUENCE_LOGIN = 'true';
      expect(isForceLoginEnabled()).toBe(true);
    });

    it('should return false when FORCE_CONFLUENCE_LOGIN is "false"', () => {
      process.env.FORCE_CONFLUENCE_LOGIN = 'false';
      expect(isForceLoginEnabled()).toBe(false);
    });

    it('should return false when FORCE_CONFLUENCE_LOGIN is undefined', () => {
      delete process.env.FORCE_CONFLUENCE_LOGIN;
      expect(isForceLoginEnabled()).toBe(false);
    });

    it('should return false when FORCE_CONFLUENCE_LOGIN is empty string', () => {
      process.env.FORCE_CONFLUENCE_LOGIN = '';
      expect(isForceLoginEnabled()).toBe(false);
    });

    it('should return false when FORCE_CONFLUENCE_LOGIN is any other value', () => {
      process.env.FORCE_CONFLUENCE_LOGIN = 'yes';
      expect(isForceLoginEnabled()).toBe(false);

      process.env.FORCE_CONFLUENCE_LOGIN = '1';
      expect(isForceLoginEnabled()).toBe(false);
    });
  });

  describe('formatInitializationErrorMessage', () => {
    it('should return error messages when attempts < maxAttempts', () => {
      const result = formatInitializationErrorMessage(1, 3);

      expect(result).toEqual([
        'This could be due to:',
        '- Incorrect password',
        '- Missing or corrupted key files',
        '- File permission issues',
        'Please try again...\n'
      ]);
    });

    it('should return empty array when attempts >= maxAttempts', () => {
      const result = formatInitializationErrorMessage(3, 3);
      expect(result).toEqual([]);
    });

    it('should return empty array when attempts > maxAttempts', () => {
      const result = formatInitializationErrorMessage(4, 3);
      expect(result).toEqual([]);
    });

    it('should handle edge cases', () => {
      expect(formatInitializationErrorMessage(0, 1)).toEqual([
        'This could be due to:',
        '- Incorrect password',
        '- Missing or corrupted key files',
        '- File permission issues',
        'Please try again...\n'
      ]);

      expect(formatInitializationErrorMessage(1, 1)).toEqual([]);
    });
  });

  describe('formatPasswordPrompt', () => {
    it('should format prompt for first attempt', () => {
      const result = formatPasswordPrompt(1, 3);
      expect(result).toBe('Enter master password: ');
    });

    it('should format prompt for subsequent attempts', () => {
      const result = formatPasswordPrompt(2, 3);
      expect(result).toBe('Enter master password (attempt 2/3): ');
    });

    it('should format prompt for last attempt', () => {
      const result = formatPasswordPrompt(3, 3);
      expect(result).toBe('Enter master password (attempt 3/3): ');
    });

    it('should handle different maxAttempts values', () => {
      expect(formatPasswordPrompt(1, 5)).toBe('Enter master password: ');
      expect(formatPasswordPrompt(2, 5)).toBe('Enter master password (attempt 2/5): ');
      expect(formatPasswordPrompt(5, 5)).toBe('Enter master password (attempt 5/5): ');
    });

    it('should handle edge cases', () => {
      expect(formatPasswordPrompt(1, 1)).toBe('Enter master password: ');
      expect(formatPasswordPrompt(0, 3)).toBe('Enter master password: ');
    });
  });

  describe('areCookiesValid', () => {
    it('should return true for non-empty array', () => {
      const cookies = [{ name: 'session', value: 'abc123' }];
      expect(areCookiesValid(cookies)).toBe(true);
    });

    it('should return false for empty array', () => {
      expect(areCookiesValid([])).toBe(false);
    });

    it('should return false for null', () => {
      expect(areCookiesValid(null as any)).toBe(false);
    });

    it('should return false for undefined', () => {
      expect(areCookiesValid(undefined as any)).toBe(false);
    });

    it('should return true for array with multiple cookies', () => {
      const cookies = [
        { name: 'session', value: 'abc123' },
        { name: 'csrf', value: 'def456' }
      ];
      expect(areCookiesValid(cookies)).toBe(true);
    });

    it('should handle truthy/falsy values correctly', () => {
      expect(areCookiesValid([0] as any)).toBe(true); // Non-empty array
      expect(areCookiesValid([false] as any)).toBe(true); // Non-empty array
      expect(areCookiesValid([''] as any)).toBe(true); // Non-empty array
    });
  });

  describe('formatCredentialsList', () => {
    it('should format single credential', () => {
      const credentials = [{ name: 'Test Confluence' }];
      const result = formatCredentialsList(credentials);
      expect(result).toBe('Test Confluence');
    });

    it('should format multiple credentials', () => {
      const credentials = [
        { name: 'Test Confluence 1' },
        { name: 'Test Confluence 2' },
        { name: 'Test Confluence 3' }
      ];
      const result = formatCredentialsList(credentials);
      expect(result).toBe('Test Confluence 1, Test Confluence 2, Test Confluence 3');
    });

    it('should handle empty array', () => {
      const result = formatCredentialsList([]);
      expect(result).toBe('');
    });

    it('should handle credentials with special characters', () => {
      const credentials = [
        { name: 'Test & Company' },
        { name: 'Test "Quotes"' },
        { name: 'Test, Comma' }
      ];
      const result = formatCredentialsList(credentials);
      expect(result).toBe('Test & Company, Test "Quotes", Test, Comma');
    });

    it('should handle credentials with empty names', () => {
      const credentials = [
        { name: '' },
        { name: 'Valid Name' },
        { name: '' }
      ];
      const result = formatCredentialsList(credentials);
      expect(result).toBe(', Valid Name, ');
    });

    it('should handle credentials without name property', () => {
      const credentials = [
        { name: 'Valid' },
        {} as any,
        { name: 'Also Valid' }
      ];
      const result = formatCredentialsList(credentials);
      expect(result).toBe('Valid, , Also Valid');
    });
  });

  describe('Authentication Utility Functions', () => {
    describe('isLoginNeeded', () => {
      it('should return true when forceLogin is true', () => {
        expect(isLoginNeeded(true, [])).toBe(true);
        expect(isLoginNeeded(true, [{ name: 'cookie' }])).toBe(true);
      });

      it('should return false when forceLogin is false and validCookies exist', () => {
        expect(isLoginNeeded(false, [{ name: 'cookie' }])).toBe(false);
      });

      it('should return true when forceLogin is false and no validCookies', () => {
        expect(isLoginNeeded(false, [])).toBe(true);
        expect(isLoginNeeded(false, null as any)).toBe(true);
        expect(isLoginNeeded(false, undefined as any)).toBe(true);
      });

      it('should handle edge cases', () => {
        expect(isLoginNeeded(false, [null] as any)).toBe(false); // Non-empty array
        expect(isLoginNeeded(false, [undefined] as any)).toBe(false); // Non-empty array
      });
    });

    describe('formatLoginTimeoutError', () => {
      it('should return correct timeout error message', () => {
        const result = formatLoginTimeoutError();
        expect(result).toBe('Login timeout reached. Please try again.');
      });

      it('should always return same message', () => {
        const result1 = formatLoginTimeoutError();
        const result2 = formatLoginTimeoutError();
        expect(result1).toBe(result2);
      });

      it('should contain "timeout" and "try again"', () => {
        const result = formatLoginTimeoutError();
        expect(result).toContain('timeout');
        expect(result).toContain('try again');
      });
    });

    describe('formatLoginSuccessMessage', () => {
      it('should return correct success message', () => {
        const result = formatLoginSuccessMessage();
        expect(result).toBe('Manual login successfully detected!');
      });

      it('should always return same message', () => {
        const result1 = formatLoginSuccessMessage();
        const result2 = formatLoginSuccessMessage();
        expect(result1).toBe(result2);
      });

      it('should contain "login" and "detected"', () => {
        const result = formatLoginSuccessMessage();
        expect(result).toContain('login');
        expect(result).toContain('detected');
      });
    });

    describe('formatLoginWaitingMessage', () => {
      it('should format message with base URL', () => {
        const result = formatLoginWaitingMessage('https://example.com');
        expect(result).toBe('Navigating to https://example.com for manual login...');
      });

      it('should handle different URLs', () => {
        expect(formatLoginWaitingMessage('http://localhost:8080')).toBe('Navigating to http://localhost:8080 for manual login...');
        expect(formatLoginWaitingMessage('https://confluence.company.com')).toBe('Navigating to https://confluence.company.com for manual login...');
      });

      it('should handle empty URL', () => {
        const result = formatLoginWaitingMessage('');
        expect(result).toBe('Navigating to  for manual login...');
      });

      it('should always contain "Navigating to" and "for manual login"', () => {
        const result = formatLoginWaitingMessage('test.com');
        expect(result).toContain('Navigating to');
        expect(result).toContain('for manual login');
      });
    });

    describe('formatBrowserLaunchMessage', () => {
      it('should return correct browser launch message', () => {
        const result = formatBrowserLaunchMessage();
        expect(result).toBe('Launching browser for manual login...');
      });

      it('should always return same message', () => {
        const result1 = formatBrowserLaunchMessage();
        const result2 = formatBrowserLaunchMessage();
        expect(result1).toBe(result2);
      });

      it('should contain "browser" and "login"', () => {
        const result = formatBrowserLaunchMessage();
        expect(result).toContain('browser');
        expect(result).toContain('login');
      });
    });

    describe('formatManualLoginInstruction', () => {
      it('should return correct instruction message', () => {
        const result = formatManualLoginInstruction();
        expect(result).toBe('Please log in manually in the browser. The script is waiting...');
      });

      it('should always return same message', () => {
        const result1 = formatManualLoginInstruction();
        const result2 = formatManualLoginInstruction();
        expect(result1).toBe(result2);
      });

      it('should contain "manually" and "waiting"', () => {
        const result = formatManualLoginInstruction();
        expect(result).toContain('manually');
        expect(result).toContain('waiting');
      });
    });

    describe('calculateRemainingWaitTime', () => {
      it('should calculate remaining time correctly', () => {
        const now = Date.now();
        const startTime = now - 1000; // 1 second ago
        const maxWaitTime = 5000; // 5 seconds

        const remaining = calculateRemainingWaitTime(startTime, maxWaitTime);
        expect(remaining).toBeGreaterThan(3000); // Should be around 4 seconds
        expect(remaining).toBeLessThanOrEqual(4000);
      });

      it('should return negative for exceeded time', () => {
        const now = Date.now();
        const startTime = now - 6000; // 6 seconds ago
        const maxWaitTime = 5000; // 5 seconds

        const remaining = calculateRemainingWaitTime(startTime, maxWaitTime);
        expect(remaining).toBeLessThan(0);
      });

      it('should return full time for just started', () => {
        const now = Date.now();
        const startTime = now; // Just started
        const maxWaitTime = 5000; // 5 seconds

        const remaining = calculateRemainingWaitTime(startTime, maxWaitTime);
        expect(remaining).toBeGreaterThan(4900); // Should be close to 5000
        expect(remaining).toBeLessThanOrEqual(5000);
      });

      it('should handle zero maxWaitTime', () => {
        const now = Date.now();
        const startTime = now;
        const maxWaitTime = 0;

        const remaining = calculateRemainingWaitTime(startTime, maxWaitTime);
        expect(remaining).toBeLessThanOrEqual(0);
      });
    });

    describe('isWaitTimeExceeded', () => {
      it('should return false for time not exceeded', () => {
        const now = Date.now();
        const startTime = now - 1000; // 1 second ago
        const maxWaitTime = 5000; // 5 seconds

        expect(isWaitTimeExceeded(startTime, maxWaitTime)).toBe(false);
      });

      it('should return true for time exceeded', () => {
        const now = Date.now();
        const startTime = now - 6000; // 6 seconds ago
        const maxWaitTime = 5000; // 5 seconds

        expect(isWaitTimeExceeded(startTime, maxWaitTime)).toBe(true);
      });

      it('should return true for exactly at limit', () => {
        const now = Date.now();
        const startTime = now - 5000; // 5 seconds ago
        const maxWaitTime = 5000; // 5 seconds

        expect(isWaitTimeExceeded(startTime, maxWaitTime)).toBe(true);
      });

      it('should return false for just started', () => {
        const now = Date.now();
        const startTime = now; // Just started
        const maxWaitTime = 5000; // 5 seconds

        expect(isWaitTimeExceeded(startTime, maxWaitTime)).toBe(false);
      });

      it('should handle zero maxWaitTime', () => {
        const now = Date.now();
        const startTime = now - 1; // 1ms ago
        const maxWaitTime = 0;

        expect(isWaitTimeExceeded(startTime, maxWaitTime)).toBe(true);
      });
    });
  });

});
