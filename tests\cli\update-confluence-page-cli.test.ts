/**
 * Tests for update-confluence-page-cli.ts
 */

import {
  parseCliArguments,
  validatePageId,
  validateFilePath,
  findCredentialByIdentifier,
  processHtmlForConfluence,
  initializeCredentialsManager,
  CliArguments,
  InitializationResult
} from '../../src/cli/update-confluence-page-cli';

// Mock dependencies
jest.mock('fs/promises');
jest.mock('marked');
jest.mock('../../src/services/secure-credentials-manager');
jest.mock('../../src/services/secure-cookie-manager');
jest.mock('../../src/services/lazy-puppeteer-manager');
jest.mock('../../src/services/http-client');
jest.mock('../../src/utils/password-input');
jest.mock('../../src/utils/logger');
jest.mock('../../src/utils/performance-utils');

describe('update-confluence-page-cli', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('parseCliArguments', () => {
    it('should parse complete arguments', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', 'Test Confluence Instance'];
      const result = parseCliArguments(argv);

      expect(result).toEqual({
        pageId: '12345',
        filePath: 'test.md',
        instanceIdentifier: 'Test Confluence Instance'
      });
    });

    it('should handle missing arguments', () => {
      const argv = ['node', 'script.js'];
      const result = parseCliArguments(argv);

      expect(result).toEqual({
        pageId: undefined,
        filePath: undefined,
        instanceIdentifier: undefined
      });
    });

    it('should handle partial arguments', () => {
      const argv = ['node', 'script.js', '12345'];
      const result = parseCliArguments(argv);

      expect(result).toEqual({
        pageId: '12345',
        filePath: undefined,
        instanceIdentifier: undefined
      });
    });

    it('should clean PowerShell special characters', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', 'Test^Confluence^Instance'];
      const result = parseCliArguments(argv);

      expect(result.instanceIdentifier).toBe('TestConfluenceInstance');
    });

    it('should handle multi-word instance identifiers', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', 'My', 'Company', 'Confluence'];
      const result = parseCliArguments(argv);

      expect(result.instanceIdentifier).toBe('My Company Confluence');
    });

    it('should handle empty instance identifier', () => {
      const argv = ['node', 'script.js', '12345', 'test.md', ''];
      const result = parseCliArguments(argv);

      expect(result.instanceIdentifier).toBeUndefined();
    });
  });

  describe('validatePageId', () => {
    it('should validate correct page ID', () => {
      const result = validatePageId('12345');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject empty page ID', () => {
      const result = validatePageId('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID cannot be empty');
    });

    it('should reject non-numeric page ID', () => {
      const result = validatePageId('invalid-id');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID must be a number');
    });

    it('should reject page ID with letters', () => {
      const result = validatePageId('123abc');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID must be a number');
    });

    it('should handle whitespace-only page ID', () => {
      const result = validatePageId('   ');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Page ID cannot be empty');
    });

    it('should validate page ID with leading/trailing spaces', () => {
      const result = validatePageId('  12345  ');
      expect(result.isValid).toBe(true);
    });
  });

  describe('validateFilePath', () => {
    it('should validate .md file', () => {
      const result = validateFilePath('test.md');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate .markdown file', () => {
      const result = validateFilePath('test.markdown');
      expect(result.isValid).toBe(true);
    });

    it('should reject empty file path', () => {
      const result = validateFilePath('');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File path cannot be empty');
    });

    it('should reject non-markdown files', () => {
      const result = validateFilePath('test.txt');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File must be a Markdown file (.md or .markdown)');
    });

    it('should handle case-insensitive extensions', () => {
      expect(validateFilePath('test.MD').isValid).toBe(true);
      expect(validateFilePath('test.MARKDOWN').isValid).toBe(true);
    });

    it('should handle paths with directories', () => {
      const result = validateFilePath('./docs/readme.md');
      expect(result.isValid).toBe(true);
    });
  });

  describe('findCredentialByIdentifier', () => {
    const mockCredentials = [
      { name: 'Test Confluence', baseUrl: 'https://test.atlassian.net' },
      { name: 'Production Confluence', baseUrl: 'https://prod.atlassian.net' },
      { name: 'Dev Environment', baseUrl: 'https://dev.atlassian.net' }
    ];

    it('should find credential by exact name', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'Test Confluence');
      expect(result.credential).toEqual(mockCredentials[0]);
      expect(result.error).toBeUndefined();
    });

    it('should find credential by case-insensitive name', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'test confluence');
      expect(result.credential).toEqual(mockCredentials[0]);
    });

    it('should find credential by base URL', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'https://prod.atlassian.net');
      expect(result.credential).toEqual(mockCredentials[1]);
    });

    it('should return error for non-existent identifier', () => {
      const result = findCredentialByIdentifier(mockCredentials, 'Non-existent');
      expect(result.credential).toBeUndefined();
      expect(result.error).toContain('No credentials found for "Non-existent"');
      expect(result.error).toContain('Available instances: Test Confluence, Production Confluence, Dev Environment');
    });

    it('should return error for empty identifier', () => {
      const result = findCredentialByIdentifier(mockCredentials, '');
      expect(result.error).toBe('Instance identifier cannot be empty');
    });

    it('should return error for empty credentials array', () => {
      const result = findCredentialByIdentifier([], 'Test');
      expect(result.error).toBe('No credentials available');
    });

    it('should handle whitespace in identifier', () => {
      const result = findCredentialByIdentifier(mockCredentials, '  Test Confluence  ');
      expect(result.credential).toBeUndefined(); // Exact match required
    });
  });

  describe('processHtmlForConfluence', () => {
    it('should convert self-closing br tags', () => {
      const html = '<p>Test</p><br><p>More text</p>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Test</p><br /><p>More text</p>');
    });

    it('should convert self-closing hr tags', () => {
      const html = '<p>Before</p><hr><p>After</p>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Before</p><hr /><p>After</p>');
    });

    it('should convert img tags', () => {
      const html = '<img src="test.jpg" alt="Test">';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<img src="test.jpg" alt="Test" />');
    });

    it('should convert input tags', () => {
      const html = '<input type="text" name="test">';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<input type="text" name="test" />');
    });

    it('should preserve already closed tags', () => {
      const html = '<p>Test</p><br /><hr /><img src="test.jpg" />';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Test</p><br /><hr /><img src="test.jpg" />');
    });

    it('should handle complex HTML', () => {
      const html = '<div><p>Test</p><br><img src="image.png"><hr><input type="submit"></div>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<div><p>Test</p><br /><img src="image.png" /><hr /><input type="submit" /></div>');
    });

    it('should handle empty string', () => {
      const result = processHtmlForConfluence('');
      expect(result).toBe('');
    });

    it('should handle HTML without self-closing tags', () => {
      const html = '<p>Just a paragraph</p><div>And a div</div>';
      const result = processHtmlForConfluence(html);
      expect(result).toBe('<p>Just a paragraph</p><div>And a div</div>');
    });
  });

  describe('initializeCredentialsManager', () => {
    let mockCredentialsManager: jest.Mocked<any>;

    beforeEach(() => {
      mockCredentialsManager = {
        initialize: jest.fn()
      };
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should initialize successfully without password', async () => {
      mockCredentialsManager.initialize.mockResolvedValue(undefined);

      const result = await initializeCredentialsManager(mockCredentialsManager);

      expect(result.success).toBe(true);
      expect(result.cancelled).toBe(false);
      expect(result.masterPassword).toBeUndefined();
      expect(mockCredentialsManager.initialize).toHaveBeenCalledTimes(1);
    });

    it('should handle non-password errors', async () => {
      mockCredentialsManager.initialize.mockRejectedValue(new Error('File system error'));

      await expect(initializeCredentialsManager(mockCredentialsManager))
        .rejects.toThrow('File system error');
    });

    it('should return correct structure for success', async () => {
      mockCredentialsManager.initialize.mockResolvedValue(undefined);

      const result = await initializeCredentialsManager(mockCredentialsManager);

      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('cancelled');
      expect(result).toHaveProperty('masterPassword');
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.cancelled).toBe('boolean');
    });
  });

});
