/**
 * Tests for performance utilities
 */

import { PerformanceUtils, LazyModule, ResourceCleanup, setupProcessCleanup } from '../../src/utils/performance-utils';
import { logger } from '../../src/utils/logger';

// Mock logger to avoid console output during tests
jest.mock('../../src/utils/logger', () => ({
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('PerformanceUtils', () => {
  beforeEach(() => {
    // Clear any loaded modules and cleanup tasks before each test
    PerformanceUtils['loadedModules'].clear();
    PerformanceUtils['cleanupTasks'].clear();
    jest.clearAllMocks();
  });

  describe('createLazyModule', () => {
    it('should create a lazy module that loads on demand', async () => {
      const mockModule = { value: 'test-module' };
      const loader = jest.fn().mockResolvedValue(mockModule);

      const lazyModule = PerformanceUtils.createLazyModule('test-module', loader);

      expect(lazyModule.isLoaded()).toBe(false);
      expect(loader).not.toHaveBeenCalled();

      const result = await lazyModule.load();

      expect(result).toBe(mockModule);
      expect(lazyModule.isLoaded()).toBe(true);
      expect(loader).toHaveBeenCalledTimes(1);
    });

    it('should return cached module on subsequent loads', async () => {
      const mockModule = { value: 'test-module' };
      const loader = jest.fn().mockResolvedValue(mockModule);

      const lazyModule = PerformanceUtils.createLazyModule('test-module', loader);

      const result1 = await lazyModule.load();
      const result2 = await lazyModule.load();

      expect(result1).toBe(mockModule);
      expect(result2).toBe(mockModule);
      expect(loader).toHaveBeenCalledTimes(1);
    });

    it('should handle concurrent load requests', async () => {
      const mockModule = { value: 'test-module' };
      const loader = jest.fn().mockResolvedValue(mockModule);

      const lazyModule = PerformanceUtils.createLazyModule('test-module', loader);

      const [result1, result2] = await Promise.all([
        lazyModule.load(),
        lazyModule.load()
      ]);

      expect(result1).toBe(mockModule);
      expect(result2).toBe(mockModule);
      expect(loader).toHaveBeenCalledTimes(1);
    });

    it('should support unloading modules', async () => {
      const mockModule = { value: 'test-module' };
      const loader = jest.fn().mockResolvedValue(mockModule);
      const unloader = jest.fn().mockResolvedValue(undefined);

      const lazyModule = PerformanceUtils.createLazyModule('test-module', loader, unloader);

      await lazyModule.load();
      expect(lazyModule.isLoaded()).toBe(true);

      lazyModule.unload();
      expect(lazyModule.isLoaded()).toBe(false);
      expect(unloader).toHaveBeenCalledWith(mockModule);
    });

    it('should handle loader errors', async () => {
      const error = new Error('Load failed');
      const loader = jest.fn().mockRejectedValue(error);

      const lazyModule = PerformanceUtils.createLazyModule('test-module', loader);

      await expect(lazyModule.load()).rejects.toThrow('Load failed');
      expect(lazyModule.isLoaded()).toBe(false);
    });
  });

  describe('registerCleanup and executeCleanup', () => {
    it('should register and execute cleanup tasks', async () => {
      const cleanup1 = jest.fn().mockResolvedValue(undefined);
      const cleanup2 = jest.fn().mockResolvedValue(undefined);

      const resource1: ResourceCleanup = { cleanup: cleanup1 };
      const resource2: ResourceCleanup = { cleanup: cleanup2 };

      PerformanceUtils.registerCleanup(resource1);
      PerformanceUtils.registerCleanup(resource2);

      await PerformanceUtils.executeCleanup();

      expect(cleanup1).toHaveBeenCalled();
      expect(cleanup2).toHaveBeenCalled();
    });

    it('should handle cleanup errors gracefully', async () => {
      const cleanup1 = jest.fn().mockRejectedValue(new Error('Cleanup 1 failed'));
      const cleanup2 = jest.fn().mockResolvedValue(undefined);

      const resource1: ResourceCleanup = { cleanup: cleanup1 };
      const resource2: ResourceCleanup = { cleanup: cleanup2 };

      PerformanceUtils.registerCleanup(resource1);
      PerformanceUtils.registerCleanup(resource2);

      await PerformanceUtils.executeCleanup();

      expect(cleanup1).toHaveBeenCalled();
      expect(cleanup2).toHaveBeenCalled();
      expect(logger.error).toHaveBeenCalledWith('Error during cleanup:', expect.any(Error));
    });

    it('should clear cleanup tasks after execution', async () => {
      const cleanup = jest.fn().mockResolvedValue(undefined);
      const resource: ResourceCleanup = { cleanup };

      PerformanceUtils.registerCleanup(resource);
      await PerformanceUtils.executeCleanup();

      // Second execution should not call cleanup again
      await PerformanceUtils.executeCleanup();
      expect(cleanup).toHaveBeenCalledTimes(1);
    });
  });

  describe('withTimeout', () => {
    it('should resolve when operation completes within timeout', async () => {
      const operation = jest.fn().mockResolvedValue('success');

      const result = await PerformanceUtils.withTimeout(operation, 1000);

      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
    });

    it('should reject when operation times out', async () => {
      let timeoutId: NodeJS.Timeout | undefined;
      const operation = jest.fn().mockImplementation(() =>
        new Promise(resolve => {
          timeoutId = setTimeout(resolve, 2000);
        })
      );

      try {
        await expect(
          PerformanceUtils.withTimeout(operation, 100)
        ).rejects.toThrow('Operation timed out after 100ms');
      } finally {
        // Clean up the timeout to prevent hanging handles
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      }
    });

    it('should use custom timeout message', async () => {
      let timeoutId: NodeJS.Timeout | undefined;
      const operation = jest.fn().mockImplementation(() =>
        new Promise(resolve => {
          timeoutId = setTimeout(resolve, 2000);
        })
      );

      try {
        await expect(
          PerformanceUtils.withTimeout(operation, 100, 'Custom timeout message')
        ).rejects.toThrow('Custom timeout message');
      } finally {
        // Clean up the timeout to prevent hanging handles
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      }
    });

    it('should reject when operation throws error', async () => {
      const operation = jest.fn().mockRejectedValue(new Error('Operation failed'));

      await expect(
        PerformanceUtils.withTimeout(operation, 1000)
      ).rejects.toThrow('Operation failed');
    });
  });

  describe('debounce', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should debounce function calls', () => {
      const fn = jest.fn();
      const debouncedFn = PerformanceUtils.debounce(fn, 100);

      debouncedFn('arg1');
      debouncedFn('arg2');
      debouncedFn('arg3');

      expect(fn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(100);

      expect(fn).toHaveBeenCalledTimes(1);
      expect(fn).toHaveBeenCalledWith('arg3');
    });

    it('should reset timer on subsequent calls', () => {
      const fn = jest.fn();
      const debouncedFn = PerformanceUtils.debounce(fn, 100);

      debouncedFn('arg1');
      jest.advanceTimersByTime(50);

      debouncedFn('arg2');
      jest.advanceTimersByTime(50);

      expect(fn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(50);

      expect(fn).toHaveBeenCalledTimes(1);
      expect(fn).toHaveBeenCalledWith('arg2');
    });
  });

  describe('throttle', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should throttle function calls', () => {
      const fn = jest.fn();
      const throttledFn = PerformanceUtils.throttle(fn, 100);

      throttledFn('arg1');
      throttledFn('arg2');
      throttledFn('arg3');

      expect(fn).toHaveBeenCalledTimes(1);
      expect(fn).toHaveBeenCalledWith('arg1');

      jest.advanceTimersByTime(100);

      throttledFn('arg4');

      expect(fn).toHaveBeenCalledTimes(2);
      expect(fn).toHaveBeenCalledWith('arg4');
    });

    it('should allow calls after throttle period', () => {
      const fn = jest.fn();
      const throttledFn = PerformanceUtils.throttle(fn, 100);

      throttledFn('arg1');
      expect(fn).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(100);

      throttledFn('arg2');
      expect(fn).toHaveBeenCalledTimes(2);
    });
  });

  describe('getMemoryUsage', () => {
    it('should return memory usage information', () => {
      const usage = PerformanceUtils.getMemoryUsage();

      expect(usage).toHaveProperty('rss');
      expect(usage).toHaveProperty('heapUsed');
      expect(usage).toHaveProperty('heapTotal');
      expect(usage).toHaveProperty('external');

      expect(typeof usage.rss).toBe('number');
      expect(typeof usage.heapUsed).toBe('number');
      expect(typeof usage.heapTotal).toBe('number');
      expect(typeof usage.external).toBe('number');
    });
  });

  describe('logMemoryUsage', () => {
    it('should log formatted memory usage', () => {
      PerformanceUtils.logMemoryUsage('test-context');

      expect(logger.debug).toHaveBeenCalledWith(
        'Memory usage [test-context]:',
        expect.objectContaining({
          rss: expect.stringMatching(/\d+\.\d+ MB/),
          heapUsed: expect.stringMatching(/\d+\.\d+ MB/),
          heapTotal: expect.stringMatching(/\d+\.\d+ MB/),
          external: expect.stringMatching(/\d+\.\d+ MB/)
        })
      );
    });
  });

  describe('forceGarbageCollection', () => {
    it('should call global.gc when available', () => {
      const mockGc = jest.fn();
      (global as any).gc = mockGc;

      PerformanceUtils.forceGarbageCollection();

      expect(mockGc).toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith('Forced garbage collection');

      delete (global as any).gc;
    });

    it('should log message when gc is not available', () => {
      delete (global as any).gc;

      PerformanceUtils.forceGarbageCollection();

      expect(logger.debug).toHaveBeenCalledWith('Garbage collection not available (run with --expose-gc)');
    });
  });
});

describe('setupProcessCleanup', () => {
  let processOnSpy: jest.SpyInstance;
  let processExitSpy: jest.SpyInstance;

  beforeEach(() => {
    processOnSpy = jest.spyOn(process, 'on').mockImplementation();
    processExitSpy = jest.spyOn(process, 'exit').mockImplementation();
    jest.clearAllMocks();
  });

  afterEach(() => {
    processOnSpy.mockRestore();
    processExitSpy.mockRestore();
  });

  it('should setup process event handlers', () => {
    setupProcessCleanup();

    expect(processOnSpy).toHaveBeenCalledWith('exit', expect.any(Function));
    expect(processOnSpy).toHaveBeenCalledWith('SIGINT', expect.any(Function));
    expect(processOnSpy).toHaveBeenCalledWith('SIGTERM', expect.any(Function));
    expect(processOnSpy).toHaveBeenCalledWith('uncaughtException', expect.any(Function));
    expect(processOnSpy).toHaveBeenCalledWith('unhandledRejection', expect.any(Function));
  });

  it('should handle SIGINT signal', async () => {
    setupProcessCleanup();

    const sigintHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGINT')?.[1];
    expect(sigintHandler).toBeDefined();

    await sigintHandler();

    expect(logger.info).toHaveBeenCalledWith('Received SIGINT, cleaning up...');
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });

  it('should handle SIGTERM signal', async () => {
    setupProcessCleanup();

    const sigtermHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGTERM')?.[1];
    expect(sigtermHandler).toBeDefined();

    await sigtermHandler();

    expect(logger.info).toHaveBeenCalledWith('Received SIGTERM, cleaning up...');
    expect(processExitSpy).toHaveBeenCalledWith(0);
  });

  it('should handle uncaught exceptions', async () => {
    setupProcessCleanup();

    const uncaughtHandler = processOnSpy.mock.calls.find(call => call[0] === 'uncaughtException')?.[1];
    expect(uncaughtHandler).toBeDefined();

    const error = new Error('Test error');
    await uncaughtHandler(error);

    expect(logger.error).toHaveBeenCalledWith('Uncaught exception:', error);
    expect(processExitSpy).toHaveBeenCalledWith(1);
  });

  it('should handle unhandled rejections', async () => {
    setupProcessCleanup();

    const rejectionHandler = processOnSpy.mock.calls.find(call => call[0] === 'unhandledRejection')?.[1];
    expect(rejectionHandler).toBeDefined();

    const reason = 'Test rejection';
    const promise = Promise.resolve(); // Use resolved promise to avoid actual rejection
    await rejectionHandler(reason, promise);

    expect(logger.error).toHaveBeenCalledWith('Unhandled rejection at:', promise, 'reason:', reason);
    expect(processExitSpy).toHaveBeenCalledWith(1);
  });
});
